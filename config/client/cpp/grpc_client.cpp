#include <auth/cpp/token_source.hpp>
#include <config/client/cpp/exceptions.hpp>
#include <config/client/cpp/grpc_client.hpp>
#include <config/convert/cpp/convert.hpp>
#include <fmt/format.h>
#include <functional>
#include <set>
#include <spdlog/spdlog.h>

#define CARBON_CLOUD_URL_SUFFIX "cloud.carbonrobotics.com"

namespace carbon {
namespace config {

ConfigClient::ConfigClient(const std::string &addr, uint32_t max_backoff_ms, double backoff_multiplier)
    : addr_(addr), channel_(nullptr), max_backoff_ms_(max_backoff_ms), backoff_multiplier_(backoff_multiplier) {}

ConfigClient::~ConfigClient() {}

void ConfigClient::setup_grpc(bool reconnect_if_down) {
  // Not Thread Safe
  if (this->channel_ != nullptr && this->channel_->GetState(true) != GRPC_CHANNEL_READY) {
    spdlog::warn("Resetting Channel due to lost connection, state: {}", this->channel_->GetState(true));
    this->reset_stub();
  }
  if (this->channel_ == nullptr) {
    if (!reconnect_if_down) {
      return;
    }

    grpc::ChannelArguments ch_args;
    ch_args.SetMaxReceiveMessageSize(64 * 1024 * 1024);

    // Configure keepalive settings
    ch_args.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS, 120000);         // 2 minutes
    ch_args.SetInt(GRPC_ARG_KEEPALIVE_TIMEOUT_MS, 60000);       // 60 seconds
    ch_args.SetInt(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1); // Allow keepalive without active calls

    if (this->addr_.find(CARBON_CLOUD_URL_SUFFIX) != std::string::npos) {
      this->channel_ =
          grpc::CreateCustomChannel(this->addr_, grpc::SslCredentials(grpc::SslCredentialsOptions()), ch_args);
    } else {
      this->channel_ = grpc::CreateCustomChannel(this->addr_, grpc::InsecureChannelCredentials(), ch_args);
    }
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<proto::ConfigService::Stub>(this->channel_);
  }
  if (this->notification_stub_ == nullptr) {
    this->notification_stub_ = std::make_shared<proto::ConfigNotificationService::Stub>(this->channel_);
  }
}

std::shared_ptr<proto::ConfigService::Stub> ConfigClient::get_grpc_stub(bool reconnect_if_down) {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc(reconnect_if_down);
  return this->stub_;
}

std::shared_ptr<proto::ConfigNotificationService::Stub> ConfigClient::get_notification_stub() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc();
  return this->notification_stub_;
}

void ConfigClient::reset() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  return this->reset_stub();
}

void ConfigClient::reset_stub() {
  // Not Thread Safe
  this->stub_ = nullptr;
  this->notification_stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status ConfigClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    this->reset_stub();
    throw config_client_error(ex.what());
  }
}

bool ConfigClient::await_connection(uint64_t timeout_ms) {
  auto start = std::chrono::steady_clock::now();
  std::chrono::milliseconds chrono_timeout_ms{timeout_ms};
  uint32_t backoff_ms = 1000;
  std::chrono::duration<double> duration_s;
  while (true) {
    duration_s = std::chrono::steady_clock::now() - start;
    if (timeout_ms != 0 && duration_s > chrono_timeout_ms) {
      return false;
    }
    try {
      this->ping();
      return true;
    } catch (const std::exception &ex) {
    }
    backoff_ms = (uint32_t)(backoff_ms * this->backoff_multiplier_);
    backoff_ms = backoff_ms <= this->max_backoff_ms_ ? backoff_ms : this->max_backoff_ms_;

    if (timeout_ms != 0 &&
        (duration_s > chrono_timeout_ms || std::chrono::milliseconds(backoff_ms) > (chrono_timeout_ms - duration_s))) {
      return false;
    }
    spdlog::warn("Awaiting Connection to Robot Syncer with Backoff: {} ms", backoff_ms);
    std::this_thread::sleep_for(std::chrono::milliseconds(backoff_ms));
  }
}

void ConfigClient::ping() {
  grpc::ClientContext context;
  proto::PingRequest req;
  proto::PongResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_x(42);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::Ping, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("Ping failed", status);
  }

  if (resp.x() != 42) {
    throw config_client_error(fmt::format("Invalid Pong Value: {}, Expected: {}", resp.x(), 42));
  }
}

void ConfigClient::reconcile(std::string prefix, std::shared_ptr<ConfigTree> config_tree, std::string config_path) {
  spdlog::info("Reconciling: {}", config_path);
  auto node = config_tree->get_node(config_path);

  if (node == nullptr) {
    spdlog::error("Node Not Found for reconciliation: {} in {}", config_path, config_tree->get_name());
    return;
  }

  std::string path = fmt::format("{}/{}", prefix, config_path);

  this->set_tree(path, node);
}

void ConfigClient::fill_tree(std::string key, std::shared_ptr<ConfigTree> config_tree) {
  grpc::ClientContext context;
  proto::GetTreeRequest req;
  proto::GetTreeResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  auto stub = this->get_grpc_stub();
  spdlog::info("Fill Tree: {}", key);
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::GetTree, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("fill_tree: GetTree failed", status);
  }

  std::vector<std::string> reconciliation_trees;
  fill_tree_node(config_tree, resp.node(), false, reconciliation_trees, "");

  for (auto tree : reconciliation_trees) {
    this->reconcile(key, config_tree, tree);
  }
}

ConfigType ConfigClient::get_config_type(std::string key) {
  grpc::ClientContext context;
  proto::GetTreeRequest req;
  proto::GetTreeResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::GetTree, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("get_config_type: GetTree failed", status);
  }

  return convert_proto_type(resp.node().def().type());
}

void ConfigClient::set_tree(std::string key, std::shared_ptr<ConfigTree> tree) {
  grpc::ClientContext context;
  proto::SetTreeRequest req;
  proto::SetTreeResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  fill_proto_node(req.mutable_node(), tree);
  auto stub = this->get_grpc_stub(true);

  if (stub == nullptr) {
    // set_tree is used in reconciliation with cloud, thus if the channel is not yet set
    // we skip so we can properly handle when the cloud connection is lost
    spdlog::warn("Channel in invalid state, skipping set tree");
    return;
  }

  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::SetTree, stub, &context, req, &resp));

  // Currently Disabling this to handle missing cloud connection
  if (false && status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("set_tree: SetTree failed", status);
  }
}

std::string ConfigClient::get_addr() const { return addr_; }

void ConfigClient::set_value_internal(std::string key, proto::SetValueRequest &req) {
  grpc::ClientContext context;
  proto::SetValueResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::SetValue, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("set_value_internal: SetValue failed", status);
  }
}

template <>
void ConfigClient::set_value(std::string key, std::string val) {
  proto::SetValueRequest req;
  auto value = req.mutable_value();
  value->set_string_val(val);
  this->set_value_internal(key, req);
}

template <>
void ConfigClient::set_value(std::string key, int64_t val) {
  proto::SetValueRequest req;
  auto value = req.mutable_value();
  value->set_int64_val(val);
  this->set_value_internal(key, req);
}

template <>
void ConfigClient::set_value(std::string key, uint64_t val) {
  proto::SetValueRequest req;
  auto value = req.mutable_value();
  value->set_uint64_val(val);
  this->set_value_internal(key, req);
}

template <>
void ConfigClient::set_value(std::string key, double val) {
  proto::SetValueRequest req;
  auto value = req.mutable_value();
  value->set_float_val(val);
  this->set_value_internal(key, req);
}

template <>
void ConfigClient::set_value(std::string key, bool val) {
  proto::SetValueRequest req;
  auto value = req.mutable_value();
  value->set_bool_val(val);
  this->set_value_internal(key, req);
}

std::string ConfigClient::get_list_dump(std::string key) {
  grpc::ClientContext context;
  proto::GetLeavesRequest req;
  proto::GetLeavesResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::GetLeaves, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("get_list_dump: GetLeaves failed", status);
  }

  std::vector<proto::ConfigLeaf> leaves(resp.leaves().begin(), resp.leaves().end());
  std::sort(leaves.begin(), leaves.end(),
            [](const auto &first, const auto &second) { return first.key() < second.key(); });

  std::stringstream ss;
  ss << std::setprecision(std::numeric_limits<double>::digits10 + 1);
  for (auto node : leaves) {
    ss << "ts: " << std::setw(13) << node.value().timestamp_ms() << " " << node.key() << ": ";
    switch (node.value().value_case()) {
    case proto::ConfigValue::ValueCase::kInt64Val:
      ss << node.value().int64_val();
      break;
    case proto::ConfigValue::ValueCase::kUint64Val:
      ss << node.value().uint64_val();
      break;
    case proto::ConfigValue::ValueCase::kFloatVal:
      ss << node.value().float_val();
      break;
    case proto::ConfigValue::ValueCase::kBoolVal:
      ss << node.value().bool_val();
      break;
    case proto::ConfigValue::ValueCase::kStringVal:
      ss << node.value().string_val();
      break;
    default:
      break;
    }
    ss << std::endl;
  }

  return ss.str();
}
std::vector<std::string> ConfigClient::get_keys(std::string key) {
  grpc::ClientContext context;
  proto::GetLeavesRequest req;
  proto::GetLeavesResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::GetLeaves, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("get_keys: GetLeaves failed", status);
  }

  std::vector<proto::ConfigLeaf> leaves(resp.leaves().begin(), resp.leaves().end());
  std::sort(leaves.begin(), leaves.end(),
            [](const auto &first, const auto &second) { return first.key() < second.key(); });
  std::vector<std::string> keys;
  for (auto node : leaves) {
    keys.emplace_back(node.key());
  }
  return keys;
}

void ConfigClient::add_to_list(std::string key, std::string name) {
  grpc::ClientContext context;
  proto::AddToListRequest req;
  proto::AddToListResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  req.set_name(name);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&proto::ConfigService::Stub::AddToList, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("add_to_list: AddToList failed", status);
  }
}
void ConfigClient::remove_from_list(std::string key, std::string name) {
  grpc::ClientContext context;
  proto::RemoveFromListRequest req;
  proto::RemoveFromListResponse resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);
  req.set_key(key);
  req.set_name(name);
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&proto::ConfigService::Stub::RemoveFromList, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("remove_from_list: RemoveFromList failed", status);
  }
}

void ConfigClient::copy(std::string source, std::string target) {
  grpc::ClientContext get_tree_context;
  proto::GetTreeRequest get_tree_req;
  proto::GetTreeResponse get_tree_resp;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  get_tree_context.AddMetadata("authorization", token);
  get_tree_req.set_key(source);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(
      std::bind(&proto::ConfigService::Stub::GetTree, stub, &get_tree_context, get_tree_req, &get_tree_resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("copy: GetTree failed", status);
  }

  grpc::ClientContext set_tree_context;
  proto::SetTreeRequest set_tree_req;
  proto::SetTreeResponse set_tree_resp;
  set_tree_context.AddMetadata("authorization", token);
  set_tree_req.set_key(target);
  set_tree_req.set_allocated_node(get_tree_resp.release_node());
  const auto last_slash_pos = target.find_last_of("/");
  const auto last_token_start = last_slash_pos != std::string::npos ? last_slash_pos + 1 : 0;
  set_tree_req.mutable_node()->set_name(target.substr(last_token_start));
  touch_node(set_tree_req.mutable_node());
  status = this->exec_grpc(
      std::bind(&proto::ConfigService::Stub::SetTree, stub, &set_tree_context, set_tree_req, &set_tree_resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error::from_status("copy: SetTree failed", status);
  }
}

std::unique_ptr<::grpc::ClientReader<::carbon::config::proto::SubscriptionNotifyMessage>>
ConfigClient::subscribe(grpc::ClientContext &context, std::vector<std::string> &keys) {
  spdlog::info("Subscribing...");
  proto::SubscriptionRequest req;

  auto token = auth::TokenSource::get()->get_formatted_bearer_token();
  context.AddMetadata("authorization", token);

  for (auto &key : keys) {
    *req.add_keys() = key;
  }

  auto stub = this->get_notification_stub();
  try {
    return stub->Subscribe(&context, req);
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    spdlog::error("Subscribing error");
    this->reset_stub();
    throw config_client_error(ex.what());
  }
}

// NOT USED: to be removed or changed
void ConfigClient::cloud_upgrade(std::string robot, std::string from_template, std::string to_template) {
  grpc::ClientContext context;
  proto::UpgradeCloudConfigRequest request;
  proto::UpgradeCloudConfigResponse response;
  request.set_robot(robot);
  request.set_from_template(from_template);
  request.set_to_template(to_template);
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&proto::ConfigService::Stub::UpgradeCloudConfig, stub, &context, request, &response));
  if (status.error_code() != grpc::StatusCode::OK) {
    throw config_client_error(fmt::format("Invalid UpgradeCloudConfig Status Code: {}, Msg: {}", status.error_code(),
                                          status.error_message()));
  }
}
} // namespace config
} // namespace carbon
