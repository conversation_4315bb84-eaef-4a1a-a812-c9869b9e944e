type: "node"
children:
  velocity_mph:
    type: "float"
    default: 0.1
    complexity: "expert"
    units: "mph"
    hint: "Simulated velocity used to generate random predictions"
  game_server_enabled:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "When enabled, the game server will be able to handle requests from the game engine. The game will set sensor data like position and velocity instead of the hw sim."
  rtc_update_lw_sim:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Connect RTC HW sim to LW HW Sim ex: speed change on RTC goes to LW"
  gps_sim_enabled:
    type: "bool"
    default: true
    complexity: "expert"
    hint: "Is this simulator providing a simulated gps interface (multiple sims implement gps such as RTC so may not need it here)"
  heading_degrees:
    type: "float"
    default: 0.0
    complexity: "expert"
    units: "deg"
    hint: "Simulated heading of the robot for gps, Units: degrees (0 is true north, 90 is east)"
  gps_offset_x:
    type: "float"
    default: 1068.0
    complexity: "expert"
    units: "mm"
    default_recommended: true
    hint: "Offset from the robot center to gps, x+ is rightward of the robot"
  gps_offset_y:
    type: "float"
    default: 1397.0
    complexity: "expert"
    units: "mm"
    default_recommended: true
    hint: "Offset from the robot center to gps, y+ is forward of the robot"
  gps_offset_z:
    type: "float"
    default: -2159.0
    complexity: "expert"
    units: "mm"
    default_recommended: true
    hint: "Offset from the robot center to gps, z+ is downward of the robot"
  gps_origin_lat:
    type: "float"
    default: 47.62684451147215
    complexity: "expert"
    default_recommended: true
    hint: "Latitude where the robot starts, in degrees north of equator; default is 807 Aurora Ave N"
  gps_origin_lon:
    type: "float"
    default: -122.34409499118749
    complexity: "expert"
    default_recommended: true
    hint: "Longitude where the robot starts, in degrees east of prime meridian; default is 807 Aurora Ave N"
  gps_origin_alt:
    type: "float"
    default: 53.0
    complexity: "expert"
    default_recommended: true
    hint: "Altitude where the robot starts, in meters above reference ellipsoid; default is 807 Aurora Ave N"
  field_len:
    type: "float"
    default: 30.0
    complexity: "expert"
    units: "m"
    default_recommended: true
    hint: "Used by sim gps, distance before turning around"
  robot_turns:
    type: "bool"
    default: true
    complexity: "expert"
    hint: "If true, robot travels the length of a field then turns around (S turns)"
  wheel_diameter_in:
    type: "float"
    default: 32.3917
    complexity: "expert"
    units: "inches"
    hint: "Simulated wheel diameter"
  bed_to_btl_height_in:
    type: "float"
    default: 27
    units: "inches"
    hint: "Light bay to bed height for field generation in simulation"
  deepweed_wait_time:
    type: "uint"
    default: 100
    default_recommended: true
    complexity: "expert"
    units: "ms"
    hint: "Simulator waits for this timeout instead of running model, to similate correct timing"
  generate_random_predictions:
    type: "bool"
    default: true
    hint: "Set to false to enable replay from file"
  replay_diagnostic_name:
    type: "string"
    hint: "Name of weeding diagnostic used for replay"
  replay_diagnostic_row:
    type: "int"
    default: 2
    hint: "Row in weeding diagnostic for which to take datafiles for replay"
  predict_fps:
    type: "float"
    default: 6.7
    default_recommended: true
    units: "FPS"
    complexity: "expert"
    hint: "Rate at which Simulator generates new predict images, this limits the rate at which cv grabs them"
  safety:
    type: "node"
    children:
      overall_estop:
        type: "bool"
        default: false
      left_estop:
        type: "bool"
        default: false
      right_estop:
        type: "bool"
        default: false
      in_cab_estop:
        type: "bool"
        default: false
      interlock:
        type: "bool"
        default: true
      laser_key:
        type: "bool"
        default: true
      lifted:
        type: "bool"
        default: false
      water_protect:
        type: "bool"
        default: true
      reset_required:
        type: "bool"
        default: false
      center_estop:
        type: "bool"
        default: false
      power_button_estop:
        type: "bool"
        default: false
      left_lpsu_interlock:
        type: "bool"
        default: true
      right_lpsu_interlock:
        type: "bool"
        default: true
      debug_mode:
        type: "bool"
        default: false
      eu_compliant_version:
        type: "bool"
        default: false
  supervisory:
    type: "node"
    children:
      water_protect_status:
        type: "bool"
        default: true
      main_contactor_status_fb:
        type: "bool"
        default: true
      power_good:
        type: "bool"
        default: true
      power_bad:
        type: "bool"
        default: false
      power_very_bad:
        type: "bool"
        default: false
      lifted_status:
        type: "bool"
        default: false
      temp_humidity_status:
        type: "bool"
        default: true
      temp_status:
        type: "bool"
        default: true
      humidity_status:
        type: "bool"
        default: true
      tractor_power:
        type: "bool"
        default: true
      ac_frequency:
        type: "float"
        default: 600.0
      ac_voltage_a_b:
        type: "float"
        default: 2400.0
        units: "v"
      ac_voltage_b_c:
        type: "float"
        default: 2400.0
        units: "v"
      ac_voltage_a_c:
        type: "float"
        default: 2400.0
        units: "v"
      ac_voltage_a:
        type: "float"
        default: 2400.0
        units: "v"
      ac_voltage_b:
        type: "float"
        default: 2400.0
        units: "v"
      ac_voltage_c:
        type: "float"
        default: 2400.0
        units: "v"
      phase_power_w_3:
        type: "float"
        default: 1000
        units: "watts"
      phase_power_va_3:
        type: "float"
        default: 0
        units: "va"
      power_factor:
        type: "float"
        default: 0
      server_cabinet_temp:
        type: "float"
        default: 2000
        units: "C"
      server_cabinet_humidity:
        type: "float"
        default: 1000
      battery_voltage_12v:
        type: "float"
        default: 13000
        units: "mv"
      chiller_temp:
        type: "float"
        default: 237
        units: "C"
      chiller_set_temp:
        type: "float"
        default: 237
        units: "C"
      chiller_flow:
        type: "float"
        default: 100
      chiller_pressure:
        type: "float"
        default: 20
        units: "psi"
      chiller_conductivity:
        type: "float"
        default: 20
        units: "uS/cm"
      chiller_alarm_1:
        type: "float"
        default: 0
      chiller_alarm_2:
        type: "float"
        default: 0
      chiller_alarm_3:
        type: "float"
        default: 0
      chiller_alarm_4:
        type: "float"
        default: 0
      runtime_240v_hours:
        type: "float"
        default: 0
        units: "hours"
      row_1_server_disable:
        type: "bool"
        default: false
      row_2_server_disable:
        type: "bool"
        default: false
      row_3_server_disable:
        type: "bool"
        default: false
      row_1_scanners_disable:
        type: "bool"
        default: false
      row_2_scanners_disable:
        type: "bool"
        default: false
      row_3_scanners_disable:
        type: "bool"
        default: false
      row_1_btl_disable:
        type: "bool"
        default: false
      row_2_btl_disable:
        type: "bool"
        default: false
      row_3_btl_disable:
        type: "bool"
        default: false
      wheel_encoder_disable:
        type: "bool"
        default: false
      gps_disable:
        type: "bool"
        default: false
      strobe_disable:
        type: "bool"
        default: false
      air_conditioner_disable:
        type: "bool"
        default: false
      chiller_disable:
        type: "bool"
        default: false
      temp_bypass_disable:
        type: "bool"
        default: false
      humidity_bypass_disable:
        type: "bool"
        default: false
      temp_humidity_bypass_disable:
        type: "bool"
        default: false
  model:
    type: "node"
    children:
      crop_detection_average_probablity:
        type: "float"
        default: 1.0
        complexity: "expert"
        hint: "Probability a crop prediction is in a picture as a percent [0,1] (for mindoo)"
      crop_detection_std_dev:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "Standard deviation of the probability a crop prediction is in a picture (for mindoo)"
      weed_detection_average_probablity:
        type: "float"
        default: 1.0
        complexity: "expert"
        hint: "Probability a weed prediction is in a picture as a percent [0,1] (for mindoo)"
      weed_detection_std_dev:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "Standard deviation of the probability a weed prediction is in a picture (for mindoo)"
      intersect_driptape_probability:
        type: "float"
        default: 0.05
        complexity: "expert"
        hint: "Probability a prediction intersects with driptape"
      prob_crop_double_detected:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "Percent chance [0, 1] of crop being double detected as weed"
      prob_weed_double_detected:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "Percent chance [0, 1] of weed being double detected as crop"
      prob_weed_class_changes:
        type: "float"
        default: 0.1
        complexity: "expert"
        hint: "Percent chance [0, 1] of weed having its percent classes change each step"
      prob_weed_double_classed:
        type: "float"
        default: 0.05
        complexity: "expert"
        hint: "Percent chance [0, 1] of weed being double detected as crop"
      crop_confidence_distribution:
        type: "string"
        default: "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the confidence distribution.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all crops will have a confidence score between 0.95 and 1"
      weed_confidence_distribution:
        type: "string"
        default: "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the confidence distribution.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all weeds will have a confidence score between 0.95 and 1"
      double_detect_crop_confidence_distribution:
        type: "string"
        default: "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the confidence distribution.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all crops will have a confidence score between 0.95 and 1"
      double_detect_weed_confidence_distribution:
        type: "string"
        default: "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the confidence distribution.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all weeds will have a confidence score between 0.95 and 1"
      crop_confidence_for_weed_distribution:
        type: "string"
        default: "100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the crop confidence distribution for weeds.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all weeds will have a crop confidence score between 0.95 and 1"
      weed_confidence_for_crop_distribution:
        type: "string"
        default: "100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"
        complexity: "expert"
        hint: "A discrete list of 20 percents to represent the weed confidence distribution for crops.
        As in Grafana, each item is a weight for a confidence percentage (0.0 through 0.95).
        i.e a list of nineteen 0s and one 100 means all crops will have a weed confidence score between 0.95 and 1"
      generate_embeddings:
        type: "bool"
        defalt: false
        complexity: "expert"
        hint: "Enable to make the sim generate 2D embeddings for crops and weeds"
  field:
    type: "node"
    children:
      row_width_in:
        type: "float"
        default: 80
        units: "inches"
        complexity: "expert"
        hint: "Row width for field generation in simulator"
      beds_per_row:
        type: "int"
        default: 2
        complexity: "expert"
        hint: "Beds per row for field generation in simulation"
      bed_pos_std_dev:
        type: "float"
        default: 0
        units: "inches"
        complexity: "expert"
        hint: "Standard deviation of the placement of beds for field generation in simulation"
      bed_top_width_in:
        type: "float"
        default: 30
        units: "inches"
        complexity: "expert"
        hint: "Bed top width for field generation in simulation"
      bed_top_height_in:
        type: "float"
        default: 6
        units: "inches"
        complexity: "expert"
        hint: "Bed top height for field generation in simulation"
      bed_top_height_std_dev:
        type: "float"
        default: 0
        units: "inches"
        hint: "Standard deviation of bed top height for field generation in simulation"
      bed_top_slope_width_in:
        type: "float"
        default: 5
        units: "inches"
        complexity: "expert"
        hint: "Width of the dirt slope from furrow to bed top for field generation in simulation (not hypotenuse)"
      crop_lines_per_bed:
        type: "int"
        default: 2
        complexity: "expert"
        hint: "Number of crop lines per bed top for field generation in simulation"
      crop_line_spacing:
        type: "float"
        default: 10
        units: "inches"
        complexity: "expert"
        hint: "Space between crop lines in same bed top for field generation in simulation"
      crop_line_spacing_std_dev:
        type: "float"
        default: 0
        units: "inches"
        complexity: "expert"
        hint: "Standard deviation of the placement of crop lines for field generation in simulation"
      crop_to_crop_spacing:
        type: "float"
        default: 4
        units: "inches"
        complexity: "expert"
        hint: "Space between crops in the same line in for field generation in simulation"
      crop_to_crop_spacing_std_dev:
        type: "float"
        default: 0
        units: "inches"
        hint: "Standard deviation of the crop to crop placement in same crop line for field generation in simulation"
      drops:
        type: "int"
        default: 1
        complexity: "expert"
        hint: "Number of drops for field generation in simulation"
      drop_spacing:
        type: "float"
        default: 2
        units: "inches"
        complexity: "expert"
        hint: "space between crops in same drop for field generation in simulation"
      seed_lines_per_crop_lines:
        type: "int"
        default: 5
        complexity: "expert"
        hint: "Number of seed lines per crop line for field generation in simulation"
      seed_line_spacing:
        type: "float"
        default: 1.5
        units: "inches"
        hint: "Space between seed lines in same bed top for field generation in simulation"
      seed_line_spacing_std_dev:
        type: "float"
        default: 0
        units: "inches"
        complexity: "expert"
        hint: "Standard deviation of the placement of seed lines for field generation in simulation"
      planting_std_dev:
        type: "float"
        default: 0
        units: "inches"
        complexity: "expert"
        hint: "Standard deviation of the placement of a single crop for field generation in simulation"
      crop_name:
        type: "string"
        default: "CROP"
        complexity: "expert"
        hint: "Deprecated: [2024-03-07]"
      crop_size_in:
        type: "float"
        default: 0.5
        units: "inches"
        complexity: "expert"
        hint: "Crop size for field generation in simulation"
      crop_size_std_dev:
        type: "float"
        default: 0
        units: "inches"
        complexity: "expert"
        hint: "Standard deviation of the crop size for field generation in simulation"
      prob_weed_is_crop:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "Percent chance [0, 1] any randomly placed weed is a misplaced crop"
      prob_germination:
        type: "float"
        default: 1.0
        complexity: "expert"
        hint: "Percent chance [0, 1] of a crop germinating. 1 being they always germinate"
  weeds:
    type: "node"
    children:
      weed_density:
        type: "string"
        default: "15, 15, 15, 15, 15, 15, 15, 15"
        complexity: "expert"
        hint: "Weed density for the row, entered as a variable length list, units are weeds per square foot.
        Entering a single value will give a constant density for the entire row.
        Entering multiple values will divide the role into multiple sections with the respective density."
      ratio_on_bed_vs_furrow:
        type: "float"
        default: 0.5
        complexity: "expert"
        hint: "Ratio, as a percent [0, 1], of weeds on the bed vs the furrow, 1 is all weeds on bed"
      ratio_in_crop_line_vs_space:
        type: "float"
        default: 0.90
        complexity: "expert"
        hint: "Ratio, as a percent [0, 1], of weeds in the crop line vs the space between crop lines, 1 is all weeds in crop line"
      weed_classes:
        type: "list"
        item:
          type: "node"
          children:
            ratio:
              type: "float"
              default: 0.25
              complexity: "expert"
              hint: "Ratio of this weed type"
            ratio_small:
              type: "float"
              default: 0.25
              complexity: "expert"
              hint: "Ratio small weeds of this weed type"
            ratio_medium:
              type: "float"
              default: 0.5
              complexity: "expert"
              hint: "Ratio medium weeds of this weed type"
            ratio_large:
              type: "float"
              default: 0.25
              complexity: "expert"
              hint: "Ratio large weeds of this weed type"
            average_size_small_in:
              type: "float"
              default: 0.5
              units: "inches"
              complexity: "expert"
              hint: "average size of small weeds for this weed type"
            average_size_medium_in:
              type: "float"
              default: 1
              units: "inches"
              complexity: "expert"
              hint: "average size of medium weeds for this weed type"
            average_size_large_in:
              type: "float"
              default: 1.5
              units: "inches"
              complexity: "expert"
              hint: "average size of large weeds for this weed type"
            size_small_std_dev:
              type: "float"
              default: 0
              units: "inches"
              complexity: "expert"
              hint: "Standard deviation for the size of this weed type"
            size_medium_std_dev:
              type: "float"
              default: 0
              units: "inches"
              complexity: "expert"
              hint: "Standard deviation for the size of this weed type"
            size_large_std_dev:
              type: "float"
              default: 0
              units: "inches"
              complexity: "expert"
              hint: "Standard deviation for the size of this weed type"
