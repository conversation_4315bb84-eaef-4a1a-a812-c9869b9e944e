type: "node"
children:
  embedding_manager:
    type: "node"
    children:
      max_chip_length:
        type: "int"
        hint: "The max length of the chip to be passed to the embedding manager"
        default: 600
  embedding_classifier:
    type: "node"
    children:
      algorithm:
        type: "string"
        hint: "The algorithm for embeddings classification"
        default: "nearest_neighbor"
        choices: ["nearest_neighbor", "random_forest"]
  auto_fix_connection_timer:
    type: "uint"
    hint: "How long to wait after failed connection to camera before trying to auto power cycle (0 is disabled)"
    units: "seconds"
    default: 0
  cameras:
    type: "list"
    item:
      type: "node"
      children:
        enabled:
          type: "bool"
        serial_number:
          type: "string"
          default_recommended: false
        ip_address:
          type: "string"
        model:
          type: "string"
        vendor:
          type: "string"
        ppi:
          type: "float"
        gpu_id:
          type: "int"
          hint: "Deprecated: [2022-03-04] no longer used"
        transpose:
          type: "bool"
        burst_record_predict_save_enabled:
          type: "bool"
          default: true
        auto_brightness:
          type: "node"
          children:
            min_exposure_us:
              type: "float"
              default: 30
            max_exposure_us:
              type: "float"
              default: 500
            exposure_delta_us:
              type: "float"
              default: 2
            min_gain_db:
              type: "float"
              default: 0
            max_gain_db:
              type: "float"
              default: 15
            gain_delta_db:
              type: "float"
              default: 2.5
            max_brightness:
              type: "float"
              default: 0.85
            min_brightness:
              type: "float"
              default: 0.8
            quantile:
              type: "float"
              default: 0.8
            moving_average_count:
              type: "int"
              default: 10
            exposure_offset_increment_us:
              type: "float"
              default: 0
        white_balance:
          default_recommended: false
          type: "node"
          children:
            red:
              default_recommended: false
              type: "float"
              default: 1.0
            green:
              default_recommended: false
              type: "float"
              default: 1.0
            blue:
              default_recommended: false
              type: "float"
              default: 1.0
        gain_db:
          type: "float"
        gamma:
          type: "float"
        flip:
          type: "bool"
          default: false
        ptp:
          type: "bool"
          default: false
        strobing:
          type: "bool"
          default: false
        mirror:
          type: "bool"
          default: false
        roi:
          type: "node"
          children:
            width:
              type: "float"
            height:
              type: float
            offset_x:
              type: float
            offset_y:
              type: float
        exposure_us:
          type: "float"
          default_recommended: false
        light_source_preset:
          type: "string"
        camera_matrix:
          type: "node"
          children:
            fx:
              type: "float"
            fy:
              type: "float"
            cx:
              type: "float"
            cy:
              type: "float"
        distortion_coefficients:
          type: "node"
          children:
            k1:
              type: "float"
            k2:
              type: "float"
            p1:
              type: "float"
            p2:
              type: "float"
            k3:
              type: "float"
            k4:
              type: "float"
            k5:
              type: "float"
            k6:
              type: "float"
            s1:
              type: "float"
            s2:
              type: "float"
            s3:
              type: "float"
            s4:
              type: "float"
            tau1:
              type: "float"
            tau2:
              type: "float"
        deepweed_enabled:
          type: "bool"
          default: false
        p2p_enabled:
          type: "bool"
          default: false
        furrows_enabled:
          type: "bool"
          default: false
        buffer_enabled:
          type: "bool"
          default: false
        buffer_max_size:
          type: "int"
          default: 200
        p2p_capture_buffer_size:
          type: "uint"
          default: 50
        p2p_capture_max_age:
          type: "uint"
          default: 12
          units: "hours"
        distance_buffer_max_size:
          type: "int"
          default: 50
        distance_buffer_interval_inches:
          type: "float"
          default: 2
        focus_metric_enabled:
          type: "bool"
          default: false
        frequency_focus_metric_enabled:
          type: "bool"
          default: false
          hint: "Enable frequency-based focus metric"
        frequency_focus_metric_reduction_ratio:
          type: "int"
          default: 100
          hint: "Reduction ratio 1/n on camera stream for images to process for frequency focus metric"
        frequency_focus_metric_frequency_sample:
          type: "float"
          default: 0.5
          hint: "Frequency to sample for frequency focus metric"
        auto_brightness_enabled:
          type: "bool"
          default: false
        random_image_scoring_enabled:
          type: "bool"
          default: false
        publisher_reduction_ratio:
          type: "int"
          default: 1
        sim_fps:
          type: "float"
          default: 1.0
        stream_packet_resend:
          type: "bool"
          default: true
        line_filter_selector:
          type: "string"
          default: Deglitch
        line_filter_width:
          type: "float"
          default: 20
        is_on_primary:
          type: "bool"
          default: true
          hint: "If running on minicomputers, is this camera located on primary minicomputer"
        only_use_distance_based:
          type: "bool"
          default: false
          hint: "For Performance reasons, it may be better to use only distance based buffers"
        v4l2:
          type: "node"
          children:
            reduction_ratio:
              type: "int"
              default: 1
            enabled:
              type: "bool"
              default: false
            rescale_ratio:
              type: "float"
              default: 1.0
              hint: "Scale the image size for writing to v4l2 buffer. Helpful to decrease image to reduce streaming bandwidth requirements (requires service restart to take effect)"
            p2p_stream_enabled:
              type: "bool"
              default: false
              hint: "Stream the output of p2p with target cam and scaled predict image"
        chip_interest_scorer:
          type: "node"
          children:
            reduction_ratio:
              type: "int"
              default: 1
            overwrite_connector:
              type: "bool"
              default: false
