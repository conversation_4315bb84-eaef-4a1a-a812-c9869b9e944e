from dataclasses import dataclass, field, fields
from enum import Enum, IntEnum
from typing import Dict, List, Optional, Tuple

from dataclass_wizard import JSONWizard

from lib.common.atim.interface import ImplementState
from lib.common.geo.geojson import Position
from lib.common.messaging.message import ErrorMsg  # noqa: F401
from lib.common.messaging.message import ContentType, Empty, empty_sender_builder, msg_sender_builder
from lib.drivers.nanopb.hasselhoff_board.hasselhoff_board_connector import Gear, HHState
from lib.drivers.steering.fault import FaultDetails
from lib.rtc.messaging.message import Message
from lib.rtc.tractor_pose import RelPos, TractorDef
from tractor_ctl.autonomy.autonomy import AutonomyMsg, TaskStateDetails
from tractor_ctl.tractor_sequencer_types import SequenceDef


class MessageType(str, Enum):
    CONTROL_STATE = "CONTROL_STATE"
    REMOTE_ASSIST_REQUIRED = "REMOTE_ASSIST_REQUIRED"

    HB_REQ = "HB_REQUEST"
    HB_RESP = "HB_RESPONSE"
    SET_STEERING_REQ = "SET_STEERING_REQUEST"
    SET_STEERING_RESP = "SET_STEERING_RESPONSE"
    SET_SPEED_REQ = "SET_SPEED_REQUEST"
    SET_SPEED_RESP = "SET_SPEED_RESPONSE"
    SET_GEAR_REQ = "SET_GEAR_REQUEST"
    SET_GEAR_RESP = "SET_GEAR_RESPONSE"
    SET_STEERING_ZERO_REQ = "SET_STEERING_ZERO_REQUEST"
    SET_STEERING_ZERO_RESP = "SET_STEERING_ZERO_RESPONSE"

    SW_CONTROLLED_STEERING_REQ = "SW_CONTROLLED_STEERING_REQUEST"
    SW_CONTROLLED_STEERING_RESP = "SW_CONTROLLED_STEERING_RESPONSE"
    STEERING_FAULT_REQ = "STEERING_FAULT_REQUEST"
    STEERING_FAULT_RESP = "STEERING_FAULT_RESPONSE"
    CLEAR_STEERING_FAULT_REQ = "CLEAR_STEERING_FAULT_REQUEST"
    CLEAR_STEERING_FAULT_RESP = "CLEAR_STEERING_FAULT_RESPONSE"

    GET_CURRENT_PATH_REQ = "GET_CURRENT_PATH_REQUEST"
    GET_CURRENT_PATH_RESP = "GET_CURRENT_PATH_RESPONSE"
    GET_CURRENT_HEADING_REQ = "GET_CURRENT_HEADING_REQUEST"
    GET_CURRENT_HEADING_RESP = "GET_CURRENT_HEADING_RESPONSE"
    SET_CURRENT_HEADING_REQ = "SET_CURRENT_HEADING_REQUEST"
    SET_CURRENT_HEADING_RESP = "SET_CURRENT_HEADING_RESPONSE"
    GET_PATH_PLANNING_INPUT_REQ = "GET_PATH_PLANNING_INPUT_REQUEST"
    GET_PATH_PLANNING_INPUT_RESP = "GET_PATH_PLANNING_INPUT_RESPONSE"
    SET_CURRENT_PATH_REQ = "SET_CURRENT_PATH_REQUEST"
    SET_CURRENT_PATH_RESP = "SET_CURRENT_PATH_RESPONSE"
    SET_AUTONOMY_REQ = "SET_AUTONOMY_REQUEST"
    SET_AUTONOMY_RESP = "SET_AUTONOMY_RESPONSE"
    SET_AUTO_STEER_RANGE_REQ = "SET_AUTO_STEER_RANGE_REQUEST"
    SET_AUTO_STEER_RANGE_RESP = "SET_AUTO_STEER_RANGE_RESPONSE"
    GET_TASK_COMPLETION_STATE_REQ = "GET_TASK_COMPLETION_STATE_REQUEST"
    GET_TASK_COMPLETION_STATE_RESP = "GET_TASK_COMPLETION_STATE_RESPONSE"
    GET_TASK_START_STATE_REQ = "GET_TASK_START_STATE_REQUEST"
    GET_TASK_START_STATE_RESP = "GET_TASK_START_STATE_RESPONSE"

    SET_HH_STATE_REQ = "SET_HH_STATE_REQUEST"
    SET_HH_STATE_RESP = "SET_HH_STATE_RESPONSE"
    SET_STOP_REQ = "SET_STOP_REQUEST"  # Deprecated use hh state
    SET_STOP_RESP = "SET_STOP_RESPONSE"
    GET_RMT_LOCKOUT_STATE_REQ = "GET_REMOTE_LOCKOUT_REQUEST"
    GET_RMT_LOCKOUT_STATE_RESP = "GET_REMOTE_LOCKOUT_RESPONSE"
    SET_CONTROL_BOARD_ENABLE_REQ = "SET_CONTROL_BOARD_ENABLE_REQUEST"  # Deprecated use hh state
    SET_CONTROL_BOARD_ENABLE_RESP = "SET_CONTROL_BOARD_ENABLE_RESPONSE"
    SET_BRAKE_REQ = "SET_BRAKE_REQUEST"
    SET_BRAKE_RESP = "SET_BRAKE_RESPONSE"
    SET_SAFETY_BYPASS_REQ = "SET_SAFETY_BYPASS_REQUEST"
    SET_SAFETY_BYPASS_RESP = "SET_SAFETY_BYPASS_RESPONSE"
    GET_SAFETY_SENSOR_STATE_REQ = "GET_SAFETY_SENSOR_STATE_REQUEST"
    GET_SAFETY_SENSOR_STATE_RESP = "GET_SAFETY_SENSOR_STATE_RESPONSE"
    SET_HITCH_REQ = "SET_HITCH_REQUEST"
    SET_HITCH_RESP = "SET_HITCH_RESPONSE"
    SET_LIGHTS_REQ = "SET_LIGHTS_REQUEST"
    SET_LIGHTS_RESP = "SET_LIGHTS_RESPONSE"
    SAVE_SEQUENCE_REQ = "SAVE_SEQUENCE_REQUEST"
    SAVE_SEQUENCE_RESP = "SAVE_SEQUENCE_RESPONSE"
    RUN_SEQUENCE_REQ = "RUN_SEQUENCE_REQUEST"
    RUN_SEQUENCE_RESP = "RUN_SEQUENCE_RESPONSE"
    LIST_SEQUENCES_REQ = "LIST_SEQUENCES_REQUEST"
    LIST_SEQUENCES_RESP = "LIST_SEQUENCES_RESPONSE"
    GET_SEQUENCE_REQ = "GET_SEQUENCE_REQUEST"
    GET_SEQUENCE_RESP = "GET_SEQUENCE_RESPONSE"
    SET_ENGINE_RPM_REQ = "SET_ENGINE_RPM_REQUEST"
    SET_ENGINE_RPM_RESP = "SET_ENGINE_RPM_RESPONSE"
    SET_ATIM_REQ = "SET_ATIM_REQUEST"
    SET_ATIM_RESP = "SET_ATIM_RESPONSE"
    TRACTOR_SHUTDOWN_REQ = "TRACTOR_SHUTDOWN_REQUEST"
    TRACTOR_SHUTDOWN_RESP = "TRACTOR_SHUTDOWN_RESPONSE"
    SET_BOUNDARY_BYPASS_REQ = "SET_BOUNDARY_BYPASS_REQUEST"
    SET_BOUNDARY_BYPASS_RESP = "SET_BOUNDARY_BYPASS_RESPONSE"

    RELOAD_WHEEL_POS_REQ = "RELOAD_WHEEL_POS_REQUEST"
    RELOAD_WHEEL_POS_RESP = "RELOAD_WHEEL_POS_RESPONSE"

    # Only used for testing in zombie mode to simulate unhappy states
    SET_STEERING_FAULT_REQ = "SET_STEERING_FAULT_REQUEST"
    SET_STEERING_FAULT_RESP = "SET_STEERING_FAULT_RESPONSE"
    SEND_REMOTE_ASSIST_REQUIRED_REQ = "SEND_REMOTE_ASSIST_REQUIRED_REQUEST"
    SEND_REMOTE_ASSIST_REQUIRED_RESP = "SEND_REMOTE_ASSIST_REQUIRED_RESPONSE"


class StatusCodes(IntEnum):
    FORBIDDEN = 403


@dataclass
class StreamInfo(JSONWizard):
    time_period_ms: int
    frames: int


@dataclass
class HBMsg(JSONWizard):
    request_state: Optional[bool] = False
    stream_info: Optional[StreamInfo] = None


@dataclass
class SpeedState(JSONWizard):
    speed_mph: float


@dataclass
class GearState(JSONWizard):
    gear: Gear


@dataclass
class ToggleMsg(JSONWizard):
    enabled: bool


@dataclass
class SetIntMsg(JSONWizard):
    value: int


@dataclass
class SetSpeedMsg(JSONWizard):
    speed_mph: float


@dataclass
class SetBoolMsg(JSONWizard):
    value: bool


@dataclass
class SetFloatMsg(JSONWizard):
    value: float


@dataclass
class ControlBoardEnableMsg(JSONWizard):
    value: bool
    make_safe: bool = False


@dataclass
class CurrPathResp(JSONWizard):
    path_feature: str = "null"


@dataclass
class CurrPathPlanInputResp(JSONWizard):
    path_planning_input: str = "null"


@dataclass
class SetPathReq(JSONWizard):
    path_feature: str
    path_planning_input: str


@dataclass
class SetHeadingReq(JSONWizard):
    heading_deg: Optional[float] = None  # None means use current heading


@dataclass
class CurrHeadingResp(JSONWizard):
    heading_deg: Optional[float]
    start_point: Optional[Position]


@dataclass
class RemoteAssistanceReq(JSONWizard):
    msg: str


@dataclass
class AutoSteerRange(JSONWizard):
    min: float
    max: float


@dataclass
class SetBrakeReq(JSONWizard):
    force_left: int  # 0-100
    force_right: int  # 0-100


@dataclass
class SafetySensorStateResp(JSONWizard):
    triggered: Dict[str, bool] = field(default_factory=dict)


@dataclass
class SetHitchMsg(JSONWizard):
    lift: bool  # True is up False is down
    force: float  # not used yet will be used to set force for now can only fully lift lower


@dataclass
class SequenceId(JSONWizard):
    seq: str


@dataclass
class ListSequencesMsg(JSONWizard):
    sequences: List[str]


# --------------------------------------------------------------------------------
# State objects


@dataclass
class SteeringPosState(JSONWizard):
    pos: float


@dataclass
class SteeringSWState(JSONWizard):
    enabled: bool


@dataclass
class SpeedAutonomyState(JSONWizard):
    enabled: bool


@dataclass
class SteeringAutonomyState(JSONWizard):
    enabled: bool
    algo: int


class TractorState(IntEnum):
    UNKNOWN = 0
    ESTOPPED = 1
    ENABLED = 2
    RTC_DISABLED = 3
    SAFETY_SENSOR_TRIGGERED = 4
    STOPPED = 5
    RTC_DISABLED_IN_CAB_SWITCH_ACTIVE = 6


@dataclass
class TractorControlBoardState(JSONWizard):
    state: TractorState  # deprecated use hh_state
    hh_state: HHState
    rtc_lockout: bool
    board_fault: bool


@dataclass
class HitchState(JSONWizard):
    lift_percent: float  # 0-100 range for 0-100%


@dataclass
class SafetyBypassState(JSONWizard):
    enabled: bool


@dataclass
class SafetyState(JSONWizard):
    bypass_enabled: bool
    safety_tripped: bool


@dataclass
class TractorConfiguration(JSONWizard):
    steering_range: Tuple[float, float]  # left to right, left will be negative center is 0 and right is positive
    max_speed_mph: float  # Max speed we allow this tractor to go not necessarily max speed tractor can achieve
    max_speed_safety_bypassed_mph: float  # Max speed we allow this tractor to go when a safety system is bypassed
    front_axle_center_pos: RelPos  # position of the center of the front axle relative to GPS antenna
    tractor_def: TractorDef  # Definition of points on the tractor relative to front axle center point
    differential_braking_enabled: bool
    max_steering_angle_implement_engaged: float  # Max abs steering angle we can command when implement is on ground
    hitch_down_val: float  # value at or below which we consider the hitch to be down


@dataclass
class EngineRPMState(JSONWizard):
    rpms: int


@dataclass
class TimedImplementState(ImplementState):
    last_update: float = -1


@dataclass
class AtimState(JSONWizard):
    enabled: bool


@dataclass
class CloudRequiredState(JSONWizard):
    stopped: bool
    cloud_connected: bool


@dataclass
class BoundaryCheckerData(JSONWizard):
    state: int
    bypass_enabled: bool


@dataclass
class FuelState(JSONWizard):
    level: float


@dataclass
class ControlState(JSONWizard):
    # All values must be optional
    class _(JSONWizard.Meta):
        # skip default values for dataclass fields when `to_dict` is called
        skip_defaults = True
        recursive = False

    steering_sw: Optional[SteeringSWState] = None  # Deprecated use TractorControlBoardState instead
    steering_pos: Optional[SteeringPosState] = None
    steering_fault: Optional[FaultDetails] = None
    gear: Optional[GearState] = None
    speed: Optional[SpeedState] = None
    tractor_board_state: Optional[TractorControlBoardState] = None
    hitch_state: Optional[HitchState] = None
    safety_bypass: Optional[SafetyBypassState] = None
    autonomous_state: Optional[AutonomyMsg] = None
    tractor_config: Optional[TractorConfiguration] = None
    engine_rpm: Optional[EngineRPMState] = None
    safety: Optional[SafetyState] = None
    cloud_state: Optional[CloudRequiredState] = None
    atim: Optional[AtimState] = None
    boundary_checker: Optional[BoundaryCheckerData] = None
    implement: Optional[TimedImplementState] = None
    fuel: Optional[FuelState] = None

    def update(self, rhs: "ControlState") -> None:
        for fld in fields(self):
            if rhs.__getattribute__(fld.name) is not None:
                self.__setattr__(fld.name, rhs.__getattribute__(fld.name))


# --------------------------------------------------------------------------------

REQ_RESP_MAP: Dict[str, str] = {
    MessageType.HB_REQ: MessageType.HB_RESP,
    MessageType.SET_STEERING_REQ: MessageType.SET_STEERING_RESP,
    MessageType.SET_SPEED_REQ: MessageType.SET_SPEED_RESP,
    MessageType.SET_GEAR_REQ: MessageType.SET_GEAR_RESP,
    MessageType.SET_STEERING_ZERO_REQ: MessageType.SET_STEERING_ZERO_RESP,
    MessageType.SW_CONTROLLED_STEERING_REQ: MessageType.SW_CONTROLLED_STEERING_RESP,
    MessageType.STEERING_FAULT_REQ: MessageType.STEERING_FAULT_RESP,
    MessageType.CLEAR_STEERING_FAULT_REQ: MessageType.CLEAR_STEERING_FAULT_RESP,
    MessageType.SET_STEERING_FAULT_REQ: MessageType.SET_STEERING_FAULT_RESP,
    MessageType.GET_CURRENT_PATH_REQ: MessageType.GET_CURRENT_PATH_RESP,
    MessageType.GET_CURRENT_HEADING_REQ: MessageType.GET_CURRENT_HEADING_RESP,
    MessageType.SET_CURRENT_HEADING_REQ: MessageType.SET_CURRENT_HEADING_RESP,
    MessageType.GET_PATH_PLANNING_INPUT_REQ: MessageType.GET_PATH_PLANNING_INPUT_RESP,
    MessageType.SET_CURRENT_PATH_REQ: MessageType.SET_CURRENT_PATH_RESP,
    MessageType.SET_AUTONOMY_REQ: MessageType.SET_AUTONOMY_RESP,
    MessageType.SET_AUTO_STEER_RANGE_REQ: MessageType.SET_AUTO_STEER_RANGE_RESP,
    MessageType.SEND_REMOTE_ASSIST_REQUIRED_REQ: MessageType.SEND_REMOTE_ASSIST_REQUIRED_RESP,
    MessageType.SET_HH_STATE_REQ: MessageType.SET_HH_STATE_RESP,
    MessageType.SET_STOP_REQ: MessageType.SET_STOP_RESP,
    MessageType.GET_RMT_LOCKOUT_STATE_REQ: MessageType.GET_RMT_LOCKOUT_STATE_RESP,
    MessageType.SET_CONTROL_BOARD_ENABLE_REQ: MessageType.SET_CONTROL_BOARD_ENABLE_RESP,
    MessageType.SET_BRAKE_REQ: MessageType.SET_BRAKE_RESP,
    MessageType.SET_SAFETY_BYPASS_REQ: MessageType.SET_SAFETY_BYPASS_RESP,
    MessageType.GET_SAFETY_SENSOR_STATE_REQ: MessageType.GET_SAFETY_SENSOR_STATE_RESP,
    MessageType.SET_HITCH_REQ: MessageType.SET_HITCH_RESP,
    MessageType.SET_LIGHTS_REQ: MessageType.SET_LIGHTS_RESP,
    MessageType.RELOAD_WHEEL_POS_REQ: MessageType.RELOAD_WHEEL_POS_RESP,
    MessageType.SAVE_SEQUENCE_REQ: MessageType.SAVE_SEQUENCE_RESP,
    MessageType.RUN_SEQUENCE_REQ: MessageType.RUN_SEQUENCE_RESP,
    MessageType.LIST_SEQUENCES_REQ: MessageType.LIST_SEQUENCES_RESP,
    MessageType.GET_SEQUENCE_REQ: MessageType.GET_SEQUENCE_RESP,
    MessageType.SET_ENGINE_RPM_REQ: MessageType.SET_ENGINE_RPM_RESP,
    MessageType.SET_ATIM_REQ: MessageType.SET_ATIM_RESP,
    MessageType.TRACTOR_SHUTDOWN_REQ: MessageType.TRACTOR_SHUTDOWN_RESP,
    MessageType.SET_BOUNDARY_BYPASS_REQ: MessageType.SET_BOUNDARY_BYPASS_RESP,
    MessageType.GET_TASK_COMPLETION_STATE_REQ: MessageType.GET_TASK_COMPLETION_STATE_RESP,
    MessageType.GET_TASK_START_STATE_REQ: MessageType.GET_TASK_START_STATE_RESP,
}
REQ_MAP: Dict[str, ContentType] = {
    MessageType.HB_REQ: HBMsg,
    MessageType.SET_STEERING_REQ: SetFloatMsg,
    MessageType.SET_SPEED_REQ: SetSpeedMsg,
    MessageType.SET_GEAR_REQ: SetIntMsg,
    MessageType.SW_CONTROLLED_STEERING_REQ: ToggleMsg,
    MessageType.SET_STEERING_FAULT_REQ: FaultDetails,
    MessageType.SET_CURRENT_PATH_REQ: SetPathReq,
    MessageType.GET_CURRENT_PATH_REQ: Empty,
    MessageType.SET_CURRENT_PATH_REQ: SetHeadingReq,
    MessageType.SET_AUTONOMY_REQ: AutonomyMsg,
    MessageType.SET_AUTO_STEER_RANGE_REQ: AutoSteerRange,
    MessageType.SEND_REMOTE_ASSIST_REQUIRED_REQ: RemoteAssistanceReq,
    MessageType.SET_STOP_REQ: SetIntMsg,
    MessageType.SET_HH_STATE_REQ: SetIntMsg,
    MessageType.SET_CONTROL_BOARD_ENABLE_REQ: ControlBoardEnableMsg,
    MessageType.SET_BRAKE_REQ: SetBrakeReq,
    MessageType.SET_SAFETY_BYPASS_REQ: SetBoolMsg,
    MessageType.SET_HITCH_REQ: SetHitchMsg,
    MessageType.SET_LIGHTS_REQ: SetIntMsg,
    MessageType.RELOAD_WHEEL_POS_REQ: Empty,
    MessageType.SAVE_SEQUENCE_REQ: SequenceDef,
    MessageType.RUN_SEQUENCE_REQ: SequenceId,
    MessageType.LIST_SEQUENCES_REQ: Empty,
    MessageType.GET_SEQUENCE_REQ: SequenceId,
    MessageType.SET_ENGINE_RPM_REQ: SetIntMsg,
    MessageType.SET_ATIM_REQ: ToggleMsg,
    MessageType.TRACTOR_SHUTDOWN_REQ: Empty,
    MessageType.SET_BOUNDARY_BYPASS_REQ: ToggleMsg,
    MessageType.GET_TASK_COMPLETION_STATE_REQ: SetIntMsg,
    MessageType.GET_TASK_START_STATE_REQ: SetIntMsg,
}

RESP_MAP: Dict[str, JSONWizard] = {
    MessageType.HB_RESP: Empty,
    MessageType.SET_STEERING_RESP: Empty,
    MessageType.SET_SPEED_RESP: Empty,
    MessageType.SET_GEAR_RESP: Empty,
    MessageType.SET_STEERING_ZERO_RESP: Empty,
    MessageType.SW_CONTROLLED_STEERING_RESP: Empty,
    MessageType.STEERING_FAULT_RESP: FaultDetails,
    MessageType.CLEAR_STEERING_FAULT_RESP: Empty,
    MessageType.SET_STEERING_FAULT_RESP: Empty,
    MessageType.GET_CURRENT_PATH_RESP: CurrPathResp,
    MessageType.GET_CURRENT_HEADING_RESP: CurrHeadingResp,
    MessageType.SET_CURRENT_HEADING_RESP: Empty,
    MessageType.GET_PATH_PLANNING_INPUT_RESP: CurrPathPlanInputResp,
    MessageType.SET_CURRENT_PATH_RESP: Empty,
    MessageType.SET_AUTONOMY_RESP: Empty,
    MessageType.SET_AUTO_STEER_RANGE_RESP: Empty,
    MessageType.SEND_REMOTE_ASSIST_REQUIRED_RESP: Empty,
    MessageType.SET_STOP_RESP: Empty,
    MessageType.SET_HH_STATE_RESP: Empty,
    MessageType.GET_RMT_LOCKOUT_STATE_RESP: SetBoolMsg,
    MessageType.SET_CONTROL_BOARD_ENABLE_RESP: Empty,
    MessageType.SET_BRAKE_RESP: Empty,
    MessageType.SET_SAFETY_BYPASS_RESP: Empty,
    MessageType.GET_SAFETY_SENSOR_STATE_RESP: SafetySensorStateResp,
    MessageType.SET_HITCH_RESP: Empty,
    MessageType.SET_LIGHTS_RESP: Empty,
    MessageType.RELOAD_WHEEL_POS_RESP: Empty,
    MessageType.SAVE_SEQUENCE_RESP: Empty,
    MessageType.RUN_SEQUENCE_RESP: Empty,
    MessageType.LIST_SEQUENCES_RESP: ListSequencesMsg,
    MessageType.GET_SEQUENCE_RESP: SequenceDef,
    MessageType.SET_ENGINE_RPM_RESP: Empty,
    MessageType.SET_ATIM_RESP: Empty,
    MessageType.TRACTOR_SHUTDOWN_RESP: Empty,
    MessageType.SET_BOUNDARY_BYPASS_RESP: Empty,
    MessageType.GET_TASK_COMPLETION_STATE_RESP: TaskStateDetails,
    MessageType.GET_TASK_START_STATE_RESP: TaskStateDetails,
}

empty_sender = empty_sender_builder(Message, REQ_RESP_MAP)
msg_sender = msg_sender_builder(Message, REQ_RESP_MAP)
