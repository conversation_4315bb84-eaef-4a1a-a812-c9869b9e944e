import math
from typing import Iterable

import numpy as np
import shapely

from config.client.cpp.config_client_python import ConfigTree
from hw_client_gps_distributor.python.hw_client_gps_distributor import get_next_non_opt
from lib.common.messaging.message import ErrorMsg, ErrorMsgException, msg_decoder
from lib.common.units.angle import Angle
from lib.rtc.data_provider.data_bus_client import AUTH_READ_REQ, DATA_BUS
from lib.rtc.jobs.client import JobsServiceClient, geo_pb2, jobs_pb2
from proj_utils.pybind.proj_utils_python import Point, RelativePosCoord
from tractor_ctl.autonomy.autonomy import StateWithData, TaskStateDetails
from tractor_ctl.autonomy.path_checker import <PERSON><PERSON>hecker
from tractor_ctl.messages import MessageType, SetIntMsg, msg_sender
from tractor_ctl.tractor_if import Gear, TractorIF

GearMap = {
    jobs_pb2.TractorState.PARK: Gear.PARK,
    jobs_pb2.TractorState.REVERSE: Gear.REVERSE,
    jobs_pb2.TractorState.NEUTRAL: Gear.NEUTRAL,
    jobs_pb2.TractorState.FORWARD: Gear.FORWARD,
    jobs_pb2.TractorState.POWERZERO: Gear.POWERZERO,
}


class AutonomyCfg:
    def __init__(self, tree: ConfigTree) -> None:
        self._tree = tree
        self.hitch_raised_min = 75.0
        self.hitch_lowered_max = 25.0
        self.hitch_change_time = 5.0
        self.state_change_max_attempts = 3
        self._tree.register_callback(self.__reload)
        self.__reload()

    def __reload(self) -> None:
        self.hitch_raised_min = self._tree.get_node("hitch_raised_min").get_float_value() * 100
        self.hitch_lowered_max = self._tree.get_node("hitch_lowered_max").get_float_value() * 100
        self.hitch_change_time = self._tree.get_node("hitch_change_time").get_float_value()
        self.state_change_max_attempts = self._tree.get_node("state_change_max_attempts").get_int_value()


class TaskStateValidator:
    def __init__(self, tractor: TractorIF, jobs_client: JobsServiceClient, tree: ConfigTree) -> None:
        self._tractor = tractor
        self._jobs_client = jobs_client
        self._cfg = AutonomyCfg(tree.get_node("task_sequence_algo"))
        self._plotter = RelativePosCoord(None, None)

        DATA_BUS.register(MessageType.GET_TASK_COMPLETION_STATE_REQ, self._get_tcs, AUTH_READ_REQ)
        DATA_BUS.register(MessageType.GET_TASK_START_STATE_REQ, self._get_tss, AUTH_READ_REQ)

    @property
    def cfg(self) -> AutonomyCfg:
        return self._cfg

    @msg_sender
    @msg_decoder(SetIntMsg)
    async def _get_tcs(self, msg: SetIntMsg) -> TaskStateDetails:
        try:
            task = await self._jobs_client.get_task(msg.value)
        except Exception as ex:
            raise ErrorMsgException(
                err_msg=ErrorMsg(msg=f"Failed to fetch task {msg.value} from jobs service. Error: {ex}")
            )
        task_type = task.WhichOneof("task_details")
        if task_type == "tractor_state":
            return await self.__validate_tractor_state_complete(task)
        elif task_type == "go_to_reversible_path":
            return await self.validate_reversible_path_complete(task)
        else:
            return TaskStateDetails(unsupported=True)

    @msg_sender
    @msg_decoder(SetIntMsg)
    async def _get_tss(self, msg: SetIntMsg) -> TaskStateDetails:
        try:
            task = await self._jobs_client.get_task(msg.value)
        except Exception as ex:
            raise ErrorMsgException(
                err_msg=ErrorMsg(msg=f"Failed to fetch task {msg.value} from jobs service. Error: {ex}")
            )
        task_type = task.WhichOneof("task_details")
        if task_type == "laser_weed":
            return await self.__validate_lw_start_state(task)
        else:
            return TaskStateDetails(unsupported=True)

    async def __validate_tractor_req_state_(
        self, tractor_states: Iterable[jobs_pb2.TractorState], task_state: TaskStateDetails
    ) -> None:
        for state in tractor_states:
            state_type = state.WhichOneof("state")
            if state_type == "gear":
                if state.gear not in GearMap:
                    task_state.unsupported = True
                    return
                else:
                    gear = self._tractor.get_gear()
                    task_state.gear_state_valid = StateWithData(
                        data=int(gear.value), state=(gear == GearMap[state.gear])
                    )
            elif state_type == "hitch":
                # Only support command not arbitrary value currently
                hitch_percent = self._tractor.get_hitch_percent()
                if state.hitch.command == jobs_pb2.HitchState.HITCH_COMMAND_UNSPECIFIED:
                    task_state.unsupported = True
                    return
                elif state.hitch.command == jobs_pb2.HitchState.RAISED:
                    task_state.hitch_state_valid = StateWithData(
                        hitch_percent, hitch_percent > self._cfg.hitch_raised_min
                    )
                elif state.hitch.command == jobs_pb2.HitchState.LOWERED:
                    task_state.hitch_state_valid = StateWithData(
                        hitch_percent, hitch_percent < self._cfg.hitch_lowered_max
                    )
            else:
                task_state.unsupported = True
                return

    async def __validate_ab_line(
        self,
        task_state: TaskStateDetails,
        ab_line: geo_pb2.LineString,
        reversible: bool,
        tolerances: jobs_pb2.SpatialPathTolerance,
    ) -> None:
        pos = await get_next_non_opt(0)
        self._plotter.set_start(Point(pos.longitude, pos.latitude))
        path = [self._plotter.geo_to_rel(Point(pt.lng, pt.lat)) for pt in ab_line.points]
        assert len(path) == 2
        assert pos.dual is not None
        heading = Angle.from_degrees(pos.dual.heading_deg.value)
        start_pos = np.array([path[0].x, path[0].y])
        end_pos = np.array([path[1].x, path[1].y])

        xte = PathChecker.inf_line_xte(shapely.Point(0, 0), shapely.Point(start_pos), shapely.Point(end_pos))
        task_state.pos_xte_valid = StateWithData(xte, tolerances.crosstrack > xte)
        should_reverse, heading_error = PathChecker.get_heading_error(
            Angle.from_radians(self._plotter.azimuth_to_xy_theta(heading.radians)),
            shapely.Point(start_pos),
            shapely.Point(end_pos),
        )
        heading_error = math.degrees(heading_error)
        if should_reverse and not reversible:
            if heading_error < 0:
                heading_error -= 90
            else:
                heading_error += 90
            task_state.heading_valid = StateWithData(heading_error, False)
            return
        task_state.heading_valid = StateWithData(heading_error, tolerances.heading > abs(heading_error))
        if should_reverse:
            min_dist = float(np.linalg.norm(end_pos))
        else:
            min_dist = float(np.linalg.norm(start_pos))
        task_state.pos_dist_valid = StateWithData(min_dist, tolerances.distance > min_dist)

    async def __validate_lw_start_state(self, task: jobs_pb2.Task) -> TaskStateDetails:
        task_state = TaskStateDetails()
        await self.__validate_tractor_req_state_(task.expected_tractor_state, task_state)
        if task_state.unsupported:
            return task_state
        await self.__validate_ab_line(
            task_state, task.laser_weed.path, task.laser_weed.path_is_reversible, task.laser_weed.tolerances
        )
        return task_state

    async def __validate_tractor_state_complete(self, task: jobs_pb2.Task) -> TaskStateDetails:
        task_state = TaskStateDetails()
        await self.__validate_tractor_req_state_(task.tractor_state.state, task_state)
        return task_state

    async def validate_reversible_path_complete(self, task: jobs_pb2.Task) -> TaskStateDetails:
        task_state = TaskStateDetails()
        await self.__validate_ab_line(
            task_state, task.go_to_reversible_path.path, True, task.go_to_reversible_path.tolerances
        )
        return task_state
