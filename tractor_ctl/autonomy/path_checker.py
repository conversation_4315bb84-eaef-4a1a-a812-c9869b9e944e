import math
from enum import IntEnum, auto
from logging import getLogger
from time import time
from typing import Literal, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt
import shapely
from typing_extensions import Annotated

from generated.proto.geo import geo_pb2
from hw_client_gps_distributor.python.hw_client_gps_distributor import get_next_non_opt
from lib.common.geo.geojson import Position
from lib.common.units.angle import Angle
from lib.common.units.distance import Distance
from lib.rtc.tractor_pose import TRACTOR_POSE
from proj_utils.pybind.proj_utils_python import Point, RelativePosCoord

XY_VEC = Annotated[npt.NDArray[np.float64], Literal[2]]

LOG = getLogger(__name__)


class OnPathState(IntEnum):
    UNKNOWN = auto()
    OFF_PATH = auto()
    ON_PATH = auto()
    PATH_COMPLETE = auto()


class HeadingDeltaException(Exception):
    pass


class PathChecker:
    def __init__(
        self, max_xte: Distance, max_start_xte: Distance, max_start_dist: Distance, max_start_heading_error: Angle
    ) -> None:
        self._max_xte = max_xte.meters
        self._max_start_xte = max_start_xte.meters
        self._max_start_dist = max_start_dist.meters
        self._max_start_heading_error = max_start_heading_error.radians
        self._last_state = OnPathState.UNKNOWN

    @property
    def last_state(self) -> OnPathState:
        return self._last_state

    async def __ainit(self, grpc_ls: geo_pb2.LineString, allow_path_reverse: bool) -> None:
        cur_pos = await get_next_non_opt(int((time() - 1) * 1000))
        self._last_ts = cur_pos.timestamp_ms
        self._plotter = RelativePosCoord(Point(cur_pos.longitude, cur_pos.latitude), None)
        path = [self._ll_to_local_pt(pt.lng, pt.lat) for pt in grpc_ls.points]
        if len(path) < 2:
            raise Exception("Cannot use current path as it is not valid")
        assert cur_pos.dual is not None
        cur_heading = Angle.from_radians(
            self._plotter.azimuth_to_xy_theta(math.radians(cur_pos.dual.heading_deg.value))
        )
        should_reverse, heading_delta = self.get_heading_error(cur_heading, path[0], path[-1])
        if should_reverse and not allow_path_reverse:
            LOG.info("Current heading is to far from AB line heading")
            raise HeadingDeltaException()
        elif should_reverse:
            path.reverse()
        if heading_delta > self._max_start_heading_error:
            LOG.info("Current heading is to far from AB line heading")
            raise HeadingDeltaException()
        self._path = shapely.LineString(path)

    def _ll_to_local_pt(self, lng: float, lat: float) -> shapely.Point:
        local = self._plotter.geo_to_rel(Point(lng, lat))
        return shapely.Point(local.x, local.y)

    @staticmethod
    async def build(
        max_xte: Distance,
        max_start_xte: Distance,
        max_start_dist: Distance,
        max_start_heading_error: Angle,
        grpc_ls: geo_pb2.LineString,
        allow_path_reverse: bool,
    ) -> "PathChecker":
        p = PathChecker(max_xte, max_start_xte, max_start_dist, max_start_heading_error)
        await p.__ainit(grpc_ls, allow_path_reverse)
        return p

    @staticmethod
    def get_heading_error(heading: Angle, line_pt_a: shapely.Point, line_pt_b: shapely.Point) -> Tuple[bool, float]:
        """heading error is in radians"""
        start = np.array([line_pt_a.x, line_pt_a.y])
        end = np.array([line_pt_b.x, line_pt_b.y])
        start_to_end = end - start
        end_to_start = start - end
        start_to_end = start_to_end / np.linalg.norm(start_to_end)  # unit vec
        end_to_start = end_to_start / np.linalg.norm(end_to_start)  # unit vec
        heading_vec = np.array([math.cos(heading.radians), math.sin(heading.radians)])
        heading_vec = heading_vec / np.linalg.norm(heading_vec)  # unit vec
        delta_h_start = abs(math.acos(np.dot(heading_vec, start_to_end)))
        delta_h_end = abs(math.acos(np.dot(heading_vec, end_to_start)))
        if delta_h_start < delta_h_end:
            return (False, delta_h_start)
        else:
            return (True, delta_h_end)

    @staticmethod
    def inf_line_xte(pos: shapely.Point, line_pt_a: shapely.Point, line_pt_b: shapely.Point) -> float:
        line = [line_pt_a, line_pt_b]
        if line_pt_a.x == line_pt_b.x:
            # vertical line
            line.append(shapely.Point(line_pt_a.x, pos.y))
        else:
            m = (line_pt_b.y - line_pt_a.y) / (line_pt_b.x - line_pt_a.x)
            b = line_pt_a.y - m * line_pt_a.x
            line.append(shapely.Point(pos.x, m * pos.x + b))
            # check for horizontal line
            if m != 0:
                line.append(shapely.Point((pos.y - b) / m, pos.y))
        ls = shapely.LineString(sorted(line, key=lambda p: p.x))
        dist: float = ls.distance(pos)
        return dist

    def _validate_inf_line_start_xte(self, pos: shapely.Point) -> bool:
        a = shapely.Point(self._path.coords[0])
        c = shapely.Point(self._path.coords[1])
        return self.inf_line_xte(pos, a, c) <= self._max_start_xte

    async def validate_start_pos(self) -> bool:
        cur_pos = await get_next_non_opt(int((time() - 1) * 1000))
        assert cur_pos.dual is not None
        heading = Angle.from_degrees(cur_pos.dual.heading_deg.value)
        steering_pos = TRACTOR_POSE.get_front_axle(Position(x=cur_pos.longitude, y=cur_pos.latitude), heading)
        self._last_ts = cur_pos.timestamp_ms
        local_pos = self._ll_to_local_pt(steering_pos.longitude, steering_pos.latitude)
        dist: float = local_pos.distance(shapely.Point(self._path.coords[0]))
        if dist > self._max_start_dist:
            LOG.info(
                f"Too far from starting point we are {dist}meters from starting point, cur_pos={local_pos}, req_pos={self._path.coords[0]}"
            )
            return False
        return self._validate_inf_line_start_xte(local_pos)

    async def check_on_path(self) -> OnPathState:
        last_pos = await get_next_non_opt(self._last_ts)
        assert last_pos.dual is not None
        if last_pos.timestamp_ms > 0:
            self._last_ts = last_pos.timestamp_ms
            heading = Angle.from_degrees(last_pos.dual.heading_deg.value)
            steering_pos = TRACTOR_POSE.get_front_axle(Position(x=last_pos.longitude, y=last_pos.latitude), heading)
            heading.half_circle_normalize()
            theta = Angle.from_radians(self._plotter.azimuth_to_xy_theta(heading.radians))
            cur = self._ll_to_local_pt(steering_pos.longitude, steering_pos.latitude)
            self._last_state = self._check_on_path(cur, theta)
        else:
            self._last_state = OnPathState.UNKNOWN
        return self._last_state

    def _check_on_path(self, pos: shapely.Point, theta: Angle) -> OnPathState:
        dist = self._path.distance(pos)
        if dist > self._max_xte:
            return OnPathState.OFF_PATH
        len_on_path = self._path.project(pos, normalized=True)
        if len_on_path < 1.0:
            return OnPathState.ON_PATH
        # We are at 100% of path so need to determine if we are past end of path
        tmp = self._path.coords
        last_pt = np.array(tmp[-1])
        path_vec = np.array(tmp[-2]) - last_pt
        pos_vec = np.array((pos.x, pos.y)) - last_pt
        end_angle = angle_between(path_vec, pos_vec)
        if abs(end_angle.degrees) > 90:
            return OnPathState.PATH_COMPLETE
        else:
            return OnPathState.ON_PATH


def unit_vector(vector: XY_VEC) -> XY_VEC:
    return vector / np.linalg.norm(vector)


def angle_between(v1: XY_VEC, v2: XY_VEC) -> Angle:
    v1_u = unit_vector(v1)
    v2_u = unit_vector(v2)
    return Angle.from_radians(np.arccos(np.clip(np.dot(v1_u, v2_u), -1.0, 1.0)))
