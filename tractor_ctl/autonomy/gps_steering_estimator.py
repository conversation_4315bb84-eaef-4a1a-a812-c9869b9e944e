import asyncio
from typing import Callable, List, Literal, Optional, Protocol

import numpy as np
import numpy.typing as npt
from prometheus_client import Gauge
from typing_extensions import Annotated

from config.client.cpp.config_client_python import ConfigTree
from hw_client_gps_distributor.python.hw_client_gps_distributor import get_next
from lib.common.geo.geojson import Feature, LineString, Position
from lib.common.logging import get_logger
from lib.common.units.angle import Angle
from lib.rtc.tractor_pose import TRACTOR_POSE
from proj_utils.pybind.proj_utils_python import Point, RelativePosCoord
from tractor_ctl.autonomy.stanley_steering_controller import <PERSON><PERSON><PERSON>ringController
from tractor_ctl.autonomy.steering_estimator import AutoSteerException, PathCompleteException, SteeringEstimator
from tractor_ctl.constants import SERVICE_NAME
from tractor_ctl.tractor_if import TractorInfoIF

LOG = get_logger(__name__)

XY_POINT = Annotated[npt.NDArray[np.float64], Literal[2]]
MIN_PATH_DIST = 0.05
PATH = Annotated[npt.NDArray[np.float64], Literal["N", 2]]
_heading_gauge = Gauge(
    subsystem=SERVICE_NAME, name="gps_heading", documentation="Robot heading based on GPS", labelnames=["type"],
)


class PathProvider(Protocol):
    def get_current_path(self) -> Optional[Feature]:
        ...

    def on_path_change(self, cb: Callable[[], None]) -> None:
        ...


class GpsSteeringEstimator(SteeringEstimator):
    def __init__(self, conf_tree: ConfigTree, path_provider: PathProvider, tractor: TractorInfoIF):
        self._stanley = StanleySteeringController("gps", conf_tree.get_node("stanley"), tractor)
        self._provider = path_provider
        self._tractor = tractor
        self._path_reload_req = asyncio.Event()
        self._path_reload_req.set()  # need to set on start
        self._path: PATH = np.zeros(0)
        self._last_ts = 0
        self._last_ind = -1
        self._provider.on_path_change(self.__path_changed)

    def __path_changed(self) -> None:
        self._path_reload_req.set()

    async def __init(self) -> None:
        while True:
            try:
                pos = await get_next(0, 0)
                if pos is None:
                    raise Exception("Valid GPS data required for path planning")
                self._plotter = RelativePosCoord(Point(pos.longitude, pos.latitude), None)
                self._path_reload_req.set()
                return
            except Exception as ex:
                LOG.error(f"Failed to initialize location data {ex}")
                await asyncio.sleep(1)

    @staticmethod
    async def build(
        conf_tree: ConfigTree, path_provider: PathProvider, tractor: TractorInfoIF
    ) -> "GpsSteeringEstimator":
        g = GpsSteeringEstimator(conf_tree, path_provider, tractor)
        await g.__init()
        return g

    async def reset(self) -> None:
        self._stanley.reset()

    def _clean_and_set_path(self, path: List[Point]) -> None:
        tmp_path = np.array([np.array([p.x, p.y]) for p in path])
        delta = np.diff(tmp_path, 1, 0)  # diff between consecutive points
        dist = np.linalg.norm(delta, 2, 1)  # 2 norm of each row which is 2 norm dist of consecutive points
        mask = np.logical_and(dist > MIN_PATH_DIST, delta[:, 1] != 0)
        mask = np.insert(
            mask, 0, True
        )  # since delta is diff between 2 points size is 1 less than input path and we always want first point
        if len(tmp_path) < 2:
            raise AutoSteerException("Cannot control steering current path is not valid")
        self._path = tmp_path[mask]
        LOG.info("New path set")

    async def _set_path(self) -> None:
        feature = self._provider.get_current_path()
        if feature is None:
            raise AutoSteerException("No GPS path set, cannot do GPS path following")
        raw = await get_next(0, 0)
        if raw is not None:
            pos = Point(raw.longitude, raw.latitude)
            rel = self._plotter.geo_to_rel(pos)
            rel_np = np.array([rel.x, rel.y])
            if float(np.linalg.norm(rel_np)) > 3000.0:  # moved more that 3km we should re-center for accuracy
                self._plotter.set_start(pos)
        assert isinstance(feature.geometry, LineString), "Invalid Path feature."
        path = [self._plotter.geo_to_rel(Point(*pt)) for pt in feature.geometry.coordinates]
        self._clean_and_set_path(path)
        self._last_ind = -1

    async def get_target_angle(self) -> Optional[Angle]:
        if self._path_reload_req.is_set():
            self._path_reload_req.clear()
            await self._set_path()

        last_pos = await get_next(self._last_ts, 0)
        if last_pos is not None:
            assert last_pos.dual is not None
            self._last_ts = last_pos.timestamp_ms
            heading = Angle.from_degrees(last_pos.dual.heading_deg.value)
            steering_pos = TRACTOR_POSE.get_front_axle(Position(x=last_pos.longitude, y=last_pos.latitude), heading)
            _heading_gauge.labels("actual").set(heading.degrees)
            heading.half_circle_normalize()
            theta = Angle.from_radians(self._plotter.azimuth_to_xy_theta(heading.radians))
            cur = self._plotter.geo_to_rel(Point(steering_pos.longitude, steering_pos.latitude))
            cur_np = np.array([cur.x, cur.y])
            return self._get_target_steering_angle(cur_np, theta)
        # TODO handle cases of too many failed gps attempts
        return None

    def _next_ahead_index(self, pos: XY_POINT, start_ind: int) -> int:
        path_vecs = np.diff(self._path[start_ind:], 1, 0)  # diff between consecutive points
        pos_vecs = pos - self._path[start_ind:]
        for i in range(len(path_vecs)):
            if np.dot(path_vecs[i], pos_vecs[i]) < 0:
                return i + start_ind
        if np.dot(path_vecs[-1], pos_vecs[-1]) < 0:
            return len(self._path) - 1
        return -1

    def _get_target_steering_angle(self, pos: XY_POINT, theta: Angle) -> Optional[Angle]:
        assert len(self._path) > 1
        if self._last_ind == -1:
            ind = self._next_ahead_index(pos, 0)
        else:
            ind = self._next_ahead_index(pos, self._last_ind)

        if ind == -1:
            if self._last_ind == -1:
                raise AutoSteerException("Cannot find point on path to go to")
            raise PathCompleteException("Finished")
        if ind == 0:
            LOG.info("Extending first path segment")
            ind = 1
        else:
            self._last_ind = ind - 1
        return self._stanley.get_target_angle((self._path[ind - 1], self._path[ind]), pos, theta)
