from dataclasses import dataclass
from enum import IntEnum
from typing import Generic, Optional, TypeVar

from dataclass_wizard import JSONWizard


class SteeringAssistAlgo(IntEnum):
    NONE = 0
    GPS_PATH = 1
    CV_FURROW_FOLLOW = 2
    GPS_HEADING = 3


class AutonomyMode(IntEnum):
    UNKNOWN = 0
    REMOTE_DRIVER = 1
    TASK_AUTONOMY = 2


@dataclass
class AutonomyMsg(JSONWizard):
    mode: AutonomyMode = AutonomyMode.UNKNOWN
    steering_assist_algo: SteeringAssistAlgo = SteeringAssistAlgo.NONE


T = TypeVar("T")


@dataclass
class StateWithData(Generic[T]):
    data: T
    state: bool


@dataclass
class TaskStateDetails(JSONWizard):
    unsupported: Optional[bool] = None
    pos_dist_valid: Optional[StateWithData[float]] = None
    pos_xte_valid: Optional[StateWithData[float]] = None
    heading_valid: Optional[StateWithData[float]] = None
    gear_state_valid: Optional[StateWithData[int]] = None
    hitch_state_valid: Optional[StateWithData[float]] = None
