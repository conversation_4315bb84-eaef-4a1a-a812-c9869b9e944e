"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.duration_pb2 import (
    Duration as google___protobuf___duration_pb2___Duration,
)

from google.protobuf.empty_pb2 import (
    Empty as google___protobuf___empty_pb2___Empty,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from google.protobuf.struct_pb2 import (
    Struct as google___protobuf___struct_pb2___Struct,
)

from google.protobuf.timestamp_pb2 import (
    Timestamp as google___protobuf___timestamp_pb2___Timestamp,
)

from generated.proto.geo.geo_pb2 import (
    LineString as proto___geo___geo_pb2___LineString,
    Point as proto___geo___geo_pb2___Point,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

StateValue = typing___NewType('StateValue', builtin___int)
type___StateValue = StateValue
State: _State
class _State(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[StateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    STATE_UNSPECIFIED = typing___cast(StateValue, 0)
    PENDING = typing___cast(StateValue, 1)
    READY = typing___cast(StateValue, 2)
    IN_PROGRESS = typing___cast(StateValue, 3)
    COMPLETED = typing___cast(StateValue, 4)
    CANCELLED = typing___cast(StateValue, 5)
    PAUSED = typing___cast(StateValue, 6)
    FAILED = typing___cast(StateValue, 7)
    ACKNOWLEDGED = typing___cast(StateValue, 8)
    NEW = typing___cast(StateValue, 9)
STATE_UNSPECIFIED = typing___cast(StateValue, 0)
PENDING = typing___cast(StateValue, 1)
READY = typing___cast(StateValue, 2)
IN_PROGRESS = typing___cast(StateValue, 3)
COMPLETED = typing___cast(StateValue, 4)
CANCELLED = typing___cast(StateValue, 5)
PAUSED = typing___cast(StateValue, 6)
FAILED = typing___cast(StateValue, 7)
ACKNOWLEDGED = typing___cast(StateValue, 8)
NEW = typing___cast(StateValue, 9)

class Objective(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ObjectiveTypeValue = typing___NewType('ObjectiveTypeValue', builtin___int)
    type___ObjectiveTypeValue = ObjectiveTypeValue
    ObjectiveType: _ObjectiveType
    class _ObjectiveType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Objective.ObjectiveTypeValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        OBJECTIVE_TYPE_UNSPECIFIED = typing___cast(Objective.ObjectiveTypeValue, 0)
        LASER_WEED_ROW = typing___cast(Objective.ObjectiveTypeValue, 1)
    OBJECTIVE_TYPE_UNSPECIFIED = typing___cast(Objective.ObjectiveTypeValue, 0)
    LASER_WEED_ROW = typing___cast(Objective.ObjectiveTypeValue, 1)

    id: builtin___int = ...
    name: typing___Text = ...
    description: typing___Text = ...
    progress_percent: builtin___int = ...
    priority: builtin___int = ...
    type: type___Objective.ObjectiveTypeValue = ...

    @property
    def data(self) -> google___protobuf___struct_pb2___Struct: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        name : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        progress_percent : typing___Optional[builtin___int] = None,
        priority : typing___Optional[builtin___int] = None,
        type : typing___Optional[type___Objective.ObjectiveTypeValue] = None,
        data : typing___Optional[google___protobuf___struct_pb2___Struct] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"data",b"data"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"description",b"description",u"id",b"id",u"name",b"name",u"priority",b"priority",u"progress_percent",b"progress_percent",u"type",b"type"]) -> None: ...
type___Objective = Objective

class ObjectiveList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def objectives(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Objective]: ...

    def __init__(self,
        *,
        objectives : typing___Optional[typing___Iterable[type___Objective]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"objectives",b"objectives"]) -> None: ...
type___ObjectiveList = ObjectiveList

class ListObjectivesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    @property
    def objectives(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Objective]: ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        objectives : typing___Optional[typing___Iterable[type___Objective]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"objectives",b"objectives",u"page_token",b"page_token"]) -> None: ...
type___ListObjectivesResponse = ListObjectivesResponse

class GetNextActiveObjectiveRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    objective_id: builtin___int = ...

    def __init__(self,
        *,
        objective_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"objective_id",b"objective_id"]) -> None: ...
type___GetNextActiveObjectiveRequest = GetNextActiveObjectiveRequest

class GetNextActiveObjectiveResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def objective(self) -> type___Objective: ...

    def __init__(self,
        *,
        objective : typing___Optional[type___Objective] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"objective",b"objective"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"objective",b"objective"]) -> None: ...
type___GetNextActiveObjectiveResponse = GetNextActiveObjectiveResponse

class Task(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    name: typing___Text = ...
    status_info: typing___Text = ...
    state: type___StateValue = ...
    priority: builtin___int = ...
    objective_id: builtin___int = ...
    start_heading: builtin___float = ...
    end_heading: builtin___float = ...

    @property
    def started_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def ended_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def expected_duration(self) -> google___protobuf___duration_pb2___Duration: ...

    @property
    def expected_tractor_state(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TractorState]: ...

    @property
    def start_location(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def end_location(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def sequence(self) -> type___SequenceTask: ...

    @property
    def manual(self) -> type___ManualTask: ...

    @property
    def go_to_and_face(self) -> type___GoToAndFaceTask: ...

    @property
    def follow_path(self) -> type___FollowPathTask: ...

    @property
    def tractor_state(self) -> type___SetTractorStateTask: ...

    @property
    def laser_weed(self) -> type___LaserWeedTask: ...

    @property
    def stop_autonomy(self) -> type___StopAutonomyTask: ...

    @property
    def go_to_reversible_path(self) -> type___GoToReversiblePathTask: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        name : typing___Optional[typing___Text] = None,
        started_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        ended_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        expected_duration : typing___Optional[google___protobuf___duration_pb2___Duration] = None,
        status_info : typing___Optional[typing___Text] = None,
        expected_tractor_state : typing___Optional[typing___Iterable[type___TractorState]] = None,
        state : typing___Optional[type___StateValue] = None,
        priority : typing___Optional[builtin___int] = None,
        objective_id : typing___Optional[builtin___int] = None,
        start_location : typing___Optional[proto___geo___geo_pb2___Point] = None,
        start_heading : typing___Optional[builtin___float] = None,
        end_location : typing___Optional[proto___geo___geo_pb2___Point] = None,
        end_heading : typing___Optional[builtin___float] = None,
        sequence : typing___Optional[type___SequenceTask] = None,
        manual : typing___Optional[type___ManualTask] = None,
        go_to_and_face : typing___Optional[type___GoToAndFaceTask] = None,
        follow_path : typing___Optional[type___FollowPathTask] = None,
        tractor_state : typing___Optional[type___SetTractorStateTask] = None,
        laser_weed : typing___Optional[type___LaserWeedTask] = None,
        stop_autonomy : typing___Optional[type___StopAutonomyTask] = None,
        go_to_reversible_path : typing___Optional[type___GoToReversiblePathTask] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"end_location",b"end_location",u"ended_at",b"ended_at",u"expected_duration",b"expected_duration",u"follow_path",b"follow_path",u"go_to_and_face",b"go_to_and_face",u"go_to_reversible_path",b"go_to_reversible_path",u"laser_weed",b"laser_weed",u"manual",b"manual",u"sequence",b"sequence",u"start_location",b"start_location",u"started_at",b"started_at",u"stop_autonomy",b"stop_autonomy",u"task_details",b"task_details",u"tractor_state",b"tractor_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"end_heading",b"end_heading",u"end_location",b"end_location",u"ended_at",b"ended_at",u"expected_duration",b"expected_duration",u"expected_tractor_state",b"expected_tractor_state",u"follow_path",b"follow_path",u"go_to_and_face",b"go_to_and_face",u"go_to_reversible_path",b"go_to_reversible_path",u"id",b"id",u"laser_weed",b"laser_weed",u"manual",b"manual",u"name",b"name",u"objective_id",b"objective_id",u"priority",b"priority",u"sequence",b"sequence",u"start_heading",b"start_heading",u"start_location",b"start_location",u"started_at",b"started_at",u"state",b"state",u"status_info",b"status_info",u"stop_autonomy",b"stop_autonomy",u"task_details",b"task_details",u"tractor_state",b"tractor_state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"task_details",b"task_details"]) -> typing_extensions___Literal["sequence","manual","go_to_and_face","follow_path","tractor_state","laser_weed","stop_autonomy","go_to_reversible_path"]: ...
type___Task = Task

class StopAutonomyTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StopAutonomyTask = StopAutonomyTask

class LaserWeedTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    path_is_reversible: builtin___bool = ...
    weeding_enabled: builtin___bool = ...
    thinning_enabled: builtin___bool = ...
    manual: builtin___bool = ...

    @property
    def path(self) -> proto___geo___geo_pb2___LineString: ...

    @property
    def tolerances(self) -> type___SpatialPathTolerance: ...

    def __init__(self,
        *,
        path : typing___Optional[proto___geo___geo_pb2___LineString] = None,
        path_is_reversible : typing___Optional[builtin___bool] = None,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        thinning_enabled : typing___Optional[builtin___bool] = None,
        manual : typing___Optional[builtin___bool] = None,
        tolerances : typing___Optional[type___SpatialPathTolerance] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"path",b"path",u"tolerances",b"tolerances"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"manual",b"manual",u"path",b"path",u"path_is_reversible",b"path_is_reversible",u"thinning_enabled",b"thinning_enabled",u"tolerances",b"tolerances",u"weeding_enabled",b"weeding_enabled"]) -> None: ...
type___LaserWeedTask = LaserWeedTask

class SequenceTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    atomic: builtin___bool = ...

    @property
    def items(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Task]: ...

    def __init__(self,
        *,
        items : typing___Optional[typing___Iterable[type___Task]] = None,
        atomic : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"atomic",b"atomic",u"items",b"items"]) -> None: ...
type___SequenceTask = SequenceTask

class ManualTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    instructions: typing___Text = ...

    def __init__(self,
        *,
        instructions : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"instructions",b"instructions"]) -> None: ...
type___ManualTask = ManualTask

class GoToAndFaceTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    heading: builtin___float = ...

    @property
    def point(self) -> proto___geo___geo_pb2___Point: ...

    def __init__(self,
        *,
        point : typing___Optional[proto___geo___geo_pb2___Point] = None,
        heading : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"point",b"point"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"heading",b"heading",u"point",b"point"]) -> None: ...
type___GoToAndFaceTask = GoToAndFaceTask

class GoToReversiblePathTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def path(self) -> proto___geo___geo_pb2___LineString: ...

    @property
    def tolerances(self) -> type___SpatialPathTolerance: ...

    def __init__(self,
        *,
        path : typing___Optional[proto___geo___geo_pb2___LineString] = None,
        tolerances : typing___Optional[type___SpatialPathTolerance] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"path",b"path",u"tolerances",b"tolerances"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"path",b"path",u"tolerances",b"tolerances"]) -> None: ...
type___GoToReversiblePathTask = GoToReversiblePathTask

class FollowPathTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    stop_on_completion: builtin___bool = ...

    @property
    def path(self) -> proto___geo___geo_pb2___LineString: ...

    @property
    def speed(self) -> type___SpeedSetting: ...

    def __init__(self,
        *,
        path : typing___Optional[proto___geo___geo_pb2___LineString] = None,
        speed : typing___Optional[type___SpeedSetting] = None,
        stop_on_completion : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"path",b"path",u"speed",b"speed"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"path",b"path",u"speed",b"speed",u"stop_on_completion",b"stop_on_completion"]) -> None: ...
type___FollowPathTask = FollowPathTask

class SpeedSetting(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    constant_mph: builtin___float = ...

    @property
    def remote_operator_controlled(self) -> google___protobuf___empty_pb2___Empty: ...

    @property
    def implement_controlled(self) -> google___protobuf___empty_pb2___Empty: ...

    def __init__(self,
        *,
        constant_mph : typing___Optional[builtin___float] = None,
        remote_operator_controlled : typing___Optional[google___protobuf___empty_pb2___Empty] = None,
        implement_controlled : typing___Optional[google___protobuf___empty_pb2___Empty] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"constant_mph",b"constant_mph",u"implement_controlled",b"implement_controlled",u"remote_operator_controlled",b"remote_operator_controlled",u"speed",b"speed"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"constant_mph",b"constant_mph",u"implement_controlled",b"implement_controlled",u"remote_operator_controlled",b"remote_operator_controlled",u"speed",b"speed"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"speed",b"speed"]) -> typing_extensions___Literal["constant_mph","remote_operator_controlled","implement_controlled"]: ...
type___SpeedSetting = SpeedSetting

class SetTractorStateTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def state(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TractorState]: ...

    def __init__(self,
        *,
        state : typing___Optional[typing___Iterable[type___TractorState]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state"]) -> None: ...
type___SetTractorStateTask = SetTractorStateTask

class TractorState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    GearValue = typing___NewType('GearValue', builtin___int)
    type___GearValue = GearValue
    Gear: _Gear
    class _Gear(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[TractorState.GearValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        GEAR_UNSPECIFIED = typing___cast(TractorState.GearValue, 0)
        PARK = typing___cast(TractorState.GearValue, 1)
        REVERSE = typing___cast(TractorState.GearValue, 2)
        NEUTRAL = typing___cast(TractorState.GearValue, 3)
        FORWARD = typing___cast(TractorState.GearValue, 4)
        POWERZERO = typing___cast(TractorState.GearValue, 5)
    GEAR_UNSPECIFIED = typing___cast(TractorState.GearValue, 0)
    PARK = typing___cast(TractorState.GearValue, 1)
    REVERSE = typing___cast(TractorState.GearValue, 2)
    NEUTRAL = typing___cast(TractorState.GearValue, 3)
    FORWARD = typing___cast(TractorState.GearValue, 4)
    POWERZERO = typing___cast(TractorState.GearValue, 5)

    gear: type___TractorState.GearValue = ...

    @property
    def hitch(self) -> type___HitchState: ...

    def __init__(self,
        *,
        gear : typing___Optional[type___TractorState.GearValue] = None,
        hitch : typing___Optional[type___HitchState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"gear",b"gear",u"hitch",b"hitch",u"state",b"state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gear",b"gear",u"hitch",b"hitch",u"state",b"state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"state",b"state"]) -> typing_extensions___Literal["gear","hitch"]: ...
type___TractorState = TractorState

class HitchState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    HitchCommandValue = typing___NewType('HitchCommandValue', builtin___int)
    type___HitchCommandValue = HitchCommandValue
    HitchCommand: _HitchCommand
    class _HitchCommand(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[HitchState.HitchCommandValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        HITCH_COMMAND_UNSPECIFIED = typing___cast(HitchState.HitchCommandValue, 0)
        RAISED = typing___cast(HitchState.HitchCommandValue, 1)
        LOWERED = typing___cast(HitchState.HitchCommandValue, 2)
    HITCH_COMMAND_UNSPECIFIED = typing___cast(HitchState.HitchCommandValue, 0)
    RAISED = typing___cast(HitchState.HitchCommandValue, 1)
    LOWERED = typing___cast(HitchState.HitchCommandValue, 2)

    command: type___HitchState.HitchCommandValue = ...
    position: builtin___float = ...

    def __init__(self,
        *,
        command : typing___Optional[type___HitchState.HitchCommandValue] = None,
        position : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"command",b"command",u"position",b"position",u"state",b"state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"command",b"command",u"position",b"position",u"state",b"state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"state",b"state"]) -> typing_extensions___Literal["command","position"]: ...
type___HitchState = HitchState

class TaskList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def tasks(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Task]: ...

    def __init__(self,
        *,
        tasks : typing___Optional[typing___Iterable[type___Task]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"tasks",b"tasks"]) -> None: ...
type___TaskList = TaskList

class ListTasksResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    @property
    def tasks(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Task]: ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        tasks : typing___Optional[typing___Iterable[type___Task]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"page_token",b"page_token",u"tasks",b"tasks"]) -> None: ...
type___ListTasksResponse = ListTasksResponse

class SpatialPathTolerance(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    heading: builtin___float = ...
    crosstrack: builtin___float = ...
    distance: builtin___float = ...
    continuous_crosstrack: builtin___float = ...

    def __init__(self,
        *,
        heading : typing___Optional[builtin___float] = None,
        crosstrack : typing___Optional[builtin___float] = None,
        distance : typing___Optional[builtin___float] = None,
        continuous_crosstrack : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"continuous_crosstrack",b"continuous_crosstrack",u"crosstrack",b"crosstrack",u"distance",b"distance",u"heading",b"heading"]) -> None: ...
type___SpatialPathTolerance = SpatialPathTolerance

class Job(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    JobTypeValue = typing___NewType('JobTypeValue', builtin___int)
    type___JobTypeValue = JobTypeValue
    JobType: _JobType
    class _JobType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Job.JobTypeValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        JOB_TYPE_UNSPECIFIED = typing___cast(Job.JobTypeValue, 0)
        LASER_WEED = typing___cast(Job.JobTypeValue, 1)
    JOB_TYPE_UNSPECIFIED = typing___cast(Job.JobTypeValue, 0)
    LASER_WEED = typing___cast(Job.JobTypeValue, 1)

    id: builtin___int = ...
    name: typing___Text = ...
    state: type___StateValue = ...
    type: type___Job.JobTypeValue = ...
    work_order_id: builtin___int = ...
    farm_id: typing___Text = ...
    field_id: typing___Text = ...
    customer_id: typing___Text = ...
    priority: builtin___int = ...

    @property
    def started_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def ended_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def objectives(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Objective]: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        name : typing___Optional[typing___Text] = None,
        started_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        ended_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        objectives : typing___Optional[typing___Iterable[type___Objective]] = None,
        state : typing___Optional[type___StateValue] = None,
        type : typing___Optional[type___Job.JobTypeValue] = None,
        work_order_id : typing___Optional[builtin___int] = None,
        farm_id : typing___Optional[typing___Text] = None,
        field_id : typing___Optional[typing___Text] = None,
        customer_id : typing___Optional[typing___Text] = None,
        priority : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_work_order_id",b"_work_order_id",u"ended_at",b"ended_at",u"started_at",b"started_at",u"work_order_id",b"work_order_id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_work_order_id",b"_work_order_id",u"customer_id",b"customer_id",u"ended_at",b"ended_at",u"farm_id",b"farm_id",u"field_id",b"field_id",u"id",b"id",u"name",b"name",u"objectives",b"objectives",u"priority",b"priority",u"started_at",b"started_at",u"state",b"state",u"type",b"type",u"work_order_id",b"work_order_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_work_order_id",b"_work_order_id"]) -> typing_extensions___Literal["work_order_id"]: ...
type___Job = Job

class JobList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def jobs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Job]: ...

    def __init__(self,
        *,
        jobs : typing___Optional[typing___Iterable[type___Job]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobs",b"jobs"]) -> None: ...
type___JobList = JobList

class ListJobsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    @property
    def jobs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Job]: ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        jobs : typing___Optional[typing___Iterable[type___Job]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobs",b"jobs",u"page_token",b"page_token"]) -> None: ...
type___ListJobsResponse = ListJobsResponse

class WorkOrder(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    name: typing___Text = ...
    duration_minutes: builtin___int = ...
    state: type___StateValue = ...

    @property
    def scheduled_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def jobs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Job]: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        name : typing___Optional[typing___Text] = None,
        scheduled_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        duration_minutes : typing___Optional[builtin___int] = None,
        jobs : typing___Optional[typing___Iterable[type___Job]] = None,
        state : typing___Optional[type___StateValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"scheduled_at",b"scheduled_at"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"duration_minutes",b"duration_minutes",u"id",b"id",u"jobs",b"jobs",u"name",b"name",u"scheduled_at",b"scheduled_at",u"state",b"state"]) -> None: ...
type___WorkOrder = WorkOrder

class WorkOrderList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def work_orders(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___WorkOrder]: ...

    def __init__(self,
        *,
        work_orders : typing___Optional[typing___Iterable[type___WorkOrder]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"work_orders",b"work_orders"]) -> None: ...
type___WorkOrderList = WorkOrderList

class ListWorkOrdersResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    @property
    def work_orders(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___WorkOrder]: ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        work_orders : typing___Optional[typing___Iterable[type___WorkOrder]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"page_token",b"page_token",u"work_orders",b"work_orders"]) -> None: ...
type___ListWorkOrdersResponse = ListWorkOrdersResponse

class Intervention(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    InterventionCauseValue = typing___NewType('InterventionCauseValue', builtin___int)
    type___InterventionCauseValue = InterventionCauseValue
    InterventionCause: _InterventionCause
    class _InterventionCause(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Intervention.InterventionCauseValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        INTERVENTION_CAUSE_UNSPECIFIED = typing___cast(Intervention.InterventionCauseValue, 0)
        SENSOR_TRIGGERED = typing___cast(Intervention.InterventionCauseValue, 1)
        SAFETY_DRIVER_ACTION = typing___cast(Intervention.InterventionCauseValue, 2)
        TRACTOR_REQUEST = typing___cast(Intervention.InterventionCauseValue, 3)
    INTERVENTION_CAUSE_UNSPECIFIED = typing___cast(Intervention.InterventionCauseValue, 0)
    SENSOR_TRIGGERED = typing___cast(Intervention.InterventionCauseValue, 1)
    SAFETY_DRIVER_ACTION = typing___cast(Intervention.InterventionCauseValue, 2)
    TRACTOR_REQUEST = typing___cast(Intervention.InterventionCauseValue, 3)

    id: builtin___int = ...
    task_id: builtin___int = ...
    qualification: typing___Text = ...
    description: typing___Text = ...
    state: type___StateValue = ...
    robot_serial: typing___Text = ...
    job_id: builtin___int = ...
    cause: type___Intervention.InterventionCauseValue = ...
    priority: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        task_id : typing___Optional[builtin___int] = None,
        qualification : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        state : typing___Optional[type___StateValue] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        job_id : typing___Optional[builtin___int] = None,
        cause : typing___Optional[type___Intervention.InterventionCauseValue] = None,
        priority : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cause",b"cause",u"description",b"description",u"id",b"id",u"job_id",b"job_id",u"priority",b"priority",u"qualification",b"qualification",u"robot_serial",b"robot_serial",u"state",b"state",u"task_id",b"task_id"]) -> None: ...
type___Intervention = Intervention

class InterventionList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def intervention(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Intervention]: ...

    def __init__(self,
        *,
        intervention : typing___Optional[typing___Iterable[type___Intervention]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intervention",b"intervention"]) -> None: ...
type___InterventionList = InterventionList

class ListInterventionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_size: builtin___int = ...
    page_token: typing___Text = ...

    def __init__(self,
        *,
        page_size : typing___Optional[builtin___int] = None,
        page_token : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"page_size",b"page_size",u"page_token",b"page_token"]) -> None: ...
type___ListInterventionsRequest = ListInterventionsRequest

class ListInterventionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    @property
    def interventions(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Intervention]: ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        interventions : typing___Optional[typing___Iterable[type___Intervention]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"interventions",b"interventions",u"page_token",b"page_token"]) -> None: ...
type___ListInterventionsResponse = ListInterventionsResponse

class CreateInterventionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def intervention(self) -> type___Intervention: ...

    def __init__(self,
        *,
        intervention : typing___Optional[type___Intervention] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"intervention",b"intervention"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intervention",b"intervention"]) -> None: ...
type___CreateInterventionRequest = CreateInterventionRequest

class CreateInterventionResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def intervention(self) -> type___Intervention: ...

    def __init__(self,
        *,
        intervention : typing___Optional[type___Intervention] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"intervention",b"intervention"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intervention",b"intervention"]) -> None: ...
type___CreateInterventionResponse = CreateInterventionResponse

class GetActiveTaskRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def current_location(self) -> proto___geo___geo_pb2___Point: ...

    def __init__(self,
        *,
        current_location : typing___Optional[proto___geo___geo_pb2___Point] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current_location",b"current_location"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_location",b"current_location"]) -> None: ...
type___GetActiveTaskRequest = GetActiveTaskRequest

class GetActiveTaskResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def task(self) -> type___Task: ...

    def __init__(self,
        *,
        task : typing___Optional[type___Task] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> None: ...
type___GetActiveTaskResponse = GetActiveTaskResponse

class GetTaskRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    task_id: builtin___int = ...

    def __init__(self,
        *,
        task_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"task_id",b"task_id"]) -> None: ...
type___GetTaskRequest = GetTaskRequest

class GetTaskResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def task(self) -> type___Task: ...

    def __init__(self,
        *,
        task : typing___Optional[type___Task] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> None: ...
type___GetTaskResponse = GetTaskResponse

class UpdateTaskRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    task_id: builtin___int = ...
    state: type___StateValue = ...
    start_heading: builtin___float = ...
    end_heading: builtin___float = ...
    status_info: typing___Text = ...

    @property
    def started_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def start_location(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def ended_at(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def end_location(self) -> proto___geo___geo_pb2___Point: ...

    def __init__(self,
        *,
        task_id : typing___Optional[builtin___int] = None,
        state : typing___Optional[type___StateValue] = None,
        started_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        start_location : typing___Optional[proto___geo___geo_pb2___Point] = None,
        start_heading : typing___Optional[builtin___float] = None,
        ended_at : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        end_location : typing___Optional[proto___geo___geo_pb2___Point] = None,
        end_heading : typing___Optional[builtin___float] = None,
        status_info : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_end_heading",b"_end_heading",u"_end_location",b"_end_location",u"_ended_at",b"_ended_at",u"_start_heading",b"_start_heading",u"_start_location",b"_start_location",u"_started_at",b"_started_at",u"_state",b"_state",u"_status_info",b"_status_info",u"end_heading",b"end_heading",u"end_location",b"end_location",u"ended_at",b"ended_at",u"start_heading",b"start_heading",u"start_location",b"start_location",u"started_at",b"started_at",u"state",b"state",u"status_info",b"status_info"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_end_heading",b"_end_heading",u"_end_location",b"_end_location",u"_ended_at",b"_ended_at",u"_start_heading",b"_start_heading",u"_start_location",b"_start_location",u"_started_at",b"_started_at",u"_state",b"_state",u"_status_info",b"_status_info",u"end_heading",b"end_heading",u"end_location",b"end_location",u"ended_at",b"ended_at",u"start_heading",b"start_heading",u"start_location",b"start_location",u"started_at",b"started_at",u"state",b"state",u"status_info",b"status_info",u"task_id",b"task_id"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_end_heading",b"_end_heading"]) -> typing_extensions___Literal["end_heading"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_end_location",b"_end_location"]) -> typing_extensions___Literal["end_location"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_ended_at",b"_ended_at"]) -> typing_extensions___Literal["ended_at"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_start_heading",b"_start_heading"]) -> typing_extensions___Literal["start_heading"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_start_location",b"_start_location"]) -> typing_extensions___Literal["start_location"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_started_at",b"_started_at"]) -> typing_extensions___Literal["started_at"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_state",b"_state"]) -> typing_extensions___Literal["state"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_status_info",b"_status_info"]) -> typing_extensions___Literal["status_info"]: ...
type___UpdateTaskRequest = UpdateTaskRequest

class UpdateTaskResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def task(self) -> type___Task: ...

    def __init__(self,
        *,
        task : typing___Optional[type___Task] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> None: ...
type___UpdateTaskResponse = UpdateTaskResponse
