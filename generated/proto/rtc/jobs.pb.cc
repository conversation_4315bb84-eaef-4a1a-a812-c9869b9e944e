// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/jobs.proto

#include "proto/rtc/jobs.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace rtc {
constexpr Objective::Objective(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , data_(nullptr)
  , id_(uint64_t{0u})
  , progress_percent_(0)
  , priority_(0)
  , type_(0)
{}
struct ObjectiveDefaultTypeInternal {
  constexpr ObjectiveDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ObjectiveDefaultTypeInternal() {}
  union {
    Objective _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ObjectiveDefaultTypeInternal _Objective_default_instance_;
constexpr ObjectiveList::ObjectiveList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : objectives_(){}
struct ObjectiveListDefaultTypeInternal {
  constexpr ObjectiveListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ObjectiveListDefaultTypeInternal() {}
  union {
    ObjectiveList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ObjectiveListDefaultTypeInternal _ObjectiveList_default_instance_;
constexpr ListObjectivesResponse::ListObjectivesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : objectives_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListObjectivesResponseDefaultTypeInternal {
  constexpr ListObjectivesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListObjectivesResponseDefaultTypeInternal() {}
  union {
    ListObjectivesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListObjectivesResponseDefaultTypeInternal _ListObjectivesResponse_default_instance_;
constexpr GetNextActiveObjectiveRequest::GetNextActiveObjectiveRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : objective_id_(uint64_t{0u}){}
struct GetNextActiveObjectiveRequestDefaultTypeInternal {
  constexpr GetNextActiveObjectiveRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveObjectiveRequestDefaultTypeInternal() {}
  union {
    GetNextActiveObjectiveRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveObjectiveRequestDefaultTypeInternal _GetNextActiveObjectiveRequest_default_instance_;
constexpr GetNextActiveObjectiveResponse::GetNextActiveObjectiveResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : objective_(nullptr){}
struct GetNextActiveObjectiveResponseDefaultTypeInternal {
  constexpr GetNextActiveObjectiveResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveObjectiveResponseDefaultTypeInternal() {}
  union {
    GetNextActiveObjectiveResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveObjectiveResponseDefaultTypeInternal _GetNextActiveObjectiveResponse_default_instance_;
constexpr Task::Task(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : expected_tractor_state_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_info_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , started_at_(nullptr)
  , ended_at_(nullptr)
  , expected_duration_(nullptr)
  , start_location_(nullptr)
  , end_location_(nullptr)
  , id_(uint64_t{0u})
  , state_(0)

  , priority_(0)
  , objective_id_(uint64_t{0u})
  , start_heading_(0)
  , end_heading_(0)
  , _oneof_case_{}{}
struct TaskDefaultTypeInternal {
  constexpr TaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TaskDefaultTypeInternal() {}
  union {
    Task _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TaskDefaultTypeInternal _Task_default_instance_;
constexpr StopAutonomyTask::StopAutonomyTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct StopAutonomyTaskDefaultTypeInternal {
  constexpr StopAutonomyTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StopAutonomyTaskDefaultTypeInternal() {}
  union {
    StopAutonomyTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StopAutonomyTaskDefaultTypeInternal _StopAutonomyTask_default_instance_;
constexpr LaserWeedTask::LaserWeedTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_(nullptr)
  , tolerances_(nullptr)
  , path_is_reversible_(false)
  , weeding_enabled_(false)
  , thinning_enabled_(false)
  , manual_(false){}
struct LaserWeedTaskDefaultTypeInternal {
  constexpr LaserWeedTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserWeedTaskDefaultTypeInternal() {}
  union {
    LaserWeedTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserWeedTaskDefaultTypeInternal _LaserWeedTask_default_instance_;
constexpr SequenceTask::SequenceTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : items_()
  , atomic_(false){}
struct SequenceTaskDefaultTypeInternal {
  constexpr SequenceTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SequenceTaskDefaultTypeInternal() {}
  union {
    SequenceTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SequenceTaskDefaultTypeInternal _SequenceTask_default_instance_;
constexpr ManualTask::ManualTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : instructions_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ManualTaskDefaultTypeInternal {
  constexpr ManualTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ManualTaskDefaultTypeInternal() {}
  union {
    ManualTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ManualTaskDefaultTypeInternal _ManualTask_default_instance_;
constexpr GoToAndFaceTask::GoToAndFaceTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : point_(nullptr)
  , heading_(0){}
struct GoToAndFaceTaskDefaultTypeInternal {
  constexpr GoToAndFaceTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GoToAndFaceTaskDefaultTypeInternal() {}
  union {
    GoToAndFaceTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GoToAndFaceTaskDefaultTypeInternal _GoToAndFaceTask_default_instance_;
constexpr GoToReversiblePathTask::GoToReversiblePathTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_(nullptr)
  , tolerances_(nullptr){}
struct GoToReversiblePathTaskDefaultTypeInternal {
  constexpr GoToReversiblePathTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GoToReversiblePathTaskDefaultTypeInternal() {}
  union {
    GoToReversiblePathTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GoToReversiblePathTaskDefaultTypeInternal _GoToReversiblePathTask_default_instance_;
constexpr FollowPathTask::FollowPathTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_(nullptr)
  , speed_(nullptr)
  , stop_on_completion_(false){}
struct FollowPathTaskDefaultTypeInternal {
  constexpr FollowPathTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FollowPathTaskDefaultTypeInternal() {}
  union {
    FollowPathTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FollowPathTaskDefaultTypeInternal _FollowPathTask_default_instance_;
constexpr SpeedSetting::SpeedSetting(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct SpeedSettingDefaultTypeInternal {
  constexpr SpeedSettingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpeedSettingDefaultTypeInternal() {}
  union {
    SpeedSetting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpeedSettingDefaultTypeInternal _SpeedSetting_default_instance_;
constexpr SetTractorStateTask::SetTractorStateTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : state_(){}
struct SetTractorStateTaskDefaultTypeInternal {
  constexpr SetTractorStateTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetTractorStateTaskDefaultTypeInternal() {}
  union {
    SetTractorStateTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetTractorStateTaskDefaultTypeInternal _SetTractorStateTask_default_instance_;
constexpr TractorState::TractorState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct TractorStateDefaultTypeInternal {
  constexpr TractorStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TractorStateDefaultTypeInternal() {}
  union {
    TractorState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TractorStateDefaultTypeInternal _TractorState_default_instance_;
constexpr HitchState::HitchState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct HitchStateDefaultTypeInternal {
  constexpr HitchStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HitchStateDefaultTypeInternal() {}
  union {
    HitchState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HitchStateDefaultTypeInternal _HitchState_default_instance_;
constexpr TaskList::TaskList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : tasks_(){}
struct TaskListDefaultTypeInternal {
  constexpr TaskListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TaskListDefaultTypeInternal() {}
  union {
    TaskList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TaskListDefaultTypeInternal _TaskList_default_instance_;
constexpr ListTasksResponse::ListTasksResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : tasks_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListTasksResponseDefaultTypeInternal {
  constexpr ListTasksResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListTasksResponseDefaultTypeInternal() {}
  union {
    ListTasksResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListTasksResponseDefaultTypeInternal _ListTasksResponse_default_instance_;
constexpr SpatialPathTolerance::SpatialPathTolerance(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : heading_(0)
  , crosstrack_(0)
  , distance_(0)
  , continuous_crosstrack_(0){}
struct SpatialPathToleranceDefaultTypeInternal {
  constexpr SpatialPathToleranceDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialPathToleranceDefaultTypeInternal() {}
  union {
    SpatialPathTolerance _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialPathToleranceDefaultTypeInternal _SpatialPathTolerance_default_instance_;
constexpr Job::Job(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : objectives_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , farm_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , field_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , customer_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , started_at_(nullptr)
  , ended_at_(nullptr)
  , id_(uint64_t{0u})
  , state_(0)

  , type_(0)

  , work_order_id_(uint64_t{0u})
  , priority_(0){}
struct JobDefaultTypeInternal {
  constexpr JobDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobDefaultTypeInternal() {}
  union {
    Job _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobDefaultTypeInternal _Job_default_instance_;
constexpr JobList::JobList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobs_(){}
struct JobListDefaultTypeInternal {
  constexpr JobListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobListDefaultTypeInternal() {}
  union {
    JobList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobListDefaultTypeInternal _JobList_default_instance_;
constexpr ListJobsResponse::ListJobsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobs_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListJobsResponseDefaultTypeInternal {
  constexpr ListJobsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListJobsResponseDefaultTypeInternal() {}
  union {
    ListJobsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListJobsResponseDefaultTypeInternal _ListJobsResponse_default_instance_;
constexpr WorkOrder::WorkOrder(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobs_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , scheduled_at_(nullptr)
  , id_(uint64_t{0u})
  , duration_minutes_(0)
  , state_(0)
{}
struct WorkOrderDefaultTypeInternal {
  constexpr WorkOrderDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WorkOrderDefaultTypeInternal() {}
  union {
    WorkOrder _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WorkOrderDefaultTypeInternal _WorkOrder_default_instance_;
constexpr WorkOrderList::WorkOrderList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : work_orders_(){}
struct WorkOrderListDefaultTypeInternal {
  constexpr WorkOrderListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WorkOrderListDefaultTypeInternal() {}
  union {
    WorkOrderList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WorkOrderListDefaultTypeInternal _WorkOrderList_default_instance_;
constexpr ListWorkOrdersResponse::ListWorkOrdersResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : work_orders_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListWorkOrdersResponseDefaultTypeInternal {
  constexpr ListWorkOrdersResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListWorkOrdersResponseDefaultTypeInternal() {}
  union {
    ListWorkOrdersResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListWorkOrdersResponseDefaultTypeInternal _ListWorkOrdersResponse_default_instance_;
constexpr Intervention::Intervention(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : qualification_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(uint64_t{0u})
  , task_id_(uint64_t{0u})
  , state_(0)

  , cause_(0)

  , job_id_(uint64_t{0u})
  , priority_(0){}
struct InterventionDefaultTypeInternal {
  constexpr InterventionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~InterventionDefaultTypeInternal() {}
  union {
    Intervention _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT InterventionDefaultTypeInternal _Intervention_default_instance_;
constexpr InterventionList::InterventionList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : intervention_(){}
struct InterventionListDefaultTypeInternal {
  constexpr InterventionListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~InterventionListDefaultTypeInternal() {}
  union {
    InterventionList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT InterventionListDefaultTypeInternal _InterventionList_default_instance_;
constexpr ListInterventionsRequest::ListInterventionsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , page_size_(0){}
struct ListInterventionsRequestDefaultTypeInternal {
  constexpr ListInterventionsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListInterventionsRequestDefaultTypeInternal() {}
  union {
    ListInterventionsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListInterventionsRequestDefaultTypeInternal _ListInterventionsRequest_default_instance_;
constexpr ListInterventionsResponse::ListInterventionsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : interventions_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListInterventionsResponseDefaultTypeInternal {
  constexpr ListInterventionsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListInterventionsResponseDefaultTypeInternal() {}
  union {
    ListInterventionsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListInterventionsResponseDefaultTypeInternal _ListInterventionsResponse_default_instance_;
constexpr CreateInterventionRequest::CreateInterventionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : intervention_(nullptr){}
struct CreateInterventionRequestDefaultTypeInternal {
  constexpr CreateInterventionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateInterventionRequestDefaultTypeInternal() {}
  union {
    CreateInterventionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateInterventionRequestDefaultTypeInternal _CreateInterventionRequest_default_instance_;
constexpr CreateInterventionResponse::CreateInterventionResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : intervention_(nullptr){}
struct CreateInterventionResponseDefaultTypeInternal {
  constexpr CreateInterventionResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateInterventionResponseDefaultTypeInternal() {}
  union {
    CreateInterventionResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateInterventionResponseDefaultTypeInternal _CreateInterventionResponse_default_instance_;
constexpr GetActiveTaskRequest::GetActiveTaskRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : current_location_(nullptr){}
struct GetActiveTaskRequestDefaultTypeInternal {
  constexpr GetActiveTaskRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetActiveTaskRequestDefaultTypeInternal() {}
  union {
    GetActiveTaskRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetActiveTaskRequestDefaultTypeInternal _GetActiveTaskRequest_default_instance_;
constexpr GetActiveTaskResponse::GetActiveTaskResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_(nullptr){}
struct GetActiveTaskResponseDefaultTypeInternal {
  constexpr GetActiveTaskResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetActiveTaskResponseDefaultTypeInternal() {}
  union {
    GetActiveTaskResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetActiveTaskResponseDefaultTypeInternal _GetActiveTaskResponse_default_instance_;
constexpr GetTaskRequest::GetTaskRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_id_(uint64_t{0u}){}
struct GetTaskRequestDefaultTypeInternal {
  constexpr GetTaskRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetTaskRequestDefaultTypeInternal() {}
  union {
    GetTaskRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetTaskRequestDefaultTypeInternal _GetTaskRequest_default_instance_;
constexpr GetTaskResponse::GetTaskResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_(nullptr){}
struct GetTaskResponseDefaultTypeInternal {
  constexpr GetTaskResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetTaskResponseDefaultTypeInternal() {}
  union {
    GetTaskResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetTaskResponseDefaultTypeInternal _GetTaskResponse_default_instance_;
constexpr UpdateTaskRequest::UpdateTaskRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : status_info_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , started_at_(nullptr)
  , start_location_(nullptr)
  , ended_at_(nullptr)
  , end_location_(nullptr)
  , task_id_(uint64_t{0u})
  , start_heading_(0)
  , end_heading_(0)
  , state_(0)
{}
struct UpdateTaskRequestDefaultTypeInternal {
  constexpr UpdateTaskRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpdateTaskRequestDefaultTypeInternal() {}
  union {
    UpdateTaskRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpdateTaskRequestDefaultTypeInternal _UpdateTaskRequest_default_instance_;
constexpr UpdateTaskResponse::UpdateTaskResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_(nullptr){}
struct UpdateTaskResponseDefaultTypeInternal {
  constexpr UpdateTaskResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpdateTaskResponseDefaultTypeInternal() {}
  union {
    UpdateTaskResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpdateTaskResponseDefaultTypeInternal _UpdateTaskResponse_default_instance_;
}  // namespace rtc
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frtc_2fjobs_2eproto[38];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frtc_2fjobs_2eproto = nullptr;

const uint32_t TableStruct_proto_2frtc_2fjobs_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, progress_percent_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, priority_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Objective, data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ObjectiveList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ObjectiveList, objectives_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListObjectivesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListObjectivesResponse, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListObjectivesResponse, objectives_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetNextActiveObjectiveRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetNextActiveObjectiveRequest, objective_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetNextActiveObjectiveResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetNextActiveObjectiveResponse, objective_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, started_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, ended_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, expected_duration_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, status_info_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, expected_tractor_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, state_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, priority_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, objective_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, start_location_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, start_heading_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, end_location_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, end_heading_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Task, task_details_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StopAutonomyTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, path_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, path_is_reversible_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, weeding_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, thinning_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, manual_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LaserWeedTask, tolerances_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SequenceTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SequenceTask, items_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SequenceTask, atomic_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ManualTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ManualTask, instructions_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToAndFaceTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToAndFaceTask, point_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToAndFaceTask, heading_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToReversiblePathTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToReversiblePathTask, path_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GoToReversiblePathTask, tolerances_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::FollowPathTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::FollowPathTask, path_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::FollowPathTask, speed_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::FollowPathTask, stop_on_completion_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpeedSetting, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpeedSetting, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpeedSetting, speed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetTractorStateTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetTractorStateTask, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::TractorState, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::TractorState, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::TractorState, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::HitchState, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::HitchState, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::HitchState, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::TaskList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::TaskList, tasks_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListTasksResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListTasksResponse, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListTasksResponse, tasks_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpatialPathTolerance, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpatialPathTolerance, heading_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpatialPathTolerance, crosstrack_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpatialPathTolerance, distance_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SpatialPathTolerance, continuous_crosstrack_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, started_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, ended_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, objectives_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, state_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, work_order_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, farm_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, field_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, customer_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Job, priority_),
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  0,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::JobList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::JobList, jobs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListJobsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListJobsResponse, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListJobsResponse, jobs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, scheduled_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, duration_minutes_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, jobs_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrder, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrderList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::WorkOrderList, work_orders_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListWorkOrdersResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListWorkOrdersResponse, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListWorkOrdersResponse, work_orders_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, task_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, qualification_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, state_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, job_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, cause_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::Intervention, priority_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::InterventionList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::InterventionList, intervention_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsRequest, page_size_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsRequest, page_token_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsResponse, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListInterventionsResponse, interventions_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::CreateInterventionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::CreateInterventionRequest, intervention_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::CreateInterventionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::CreateInterventionResponse, intervention_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetActiveTaskRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetActiveTaskRequest, current_location_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetActiveTaskResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetActiveTaskResponse, task_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetTaskRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetTaskRequest, task_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetTaskResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetTaskResponse, task_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, task_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, state_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, started_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, start_location_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, start_heading_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, ended_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, end_location_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, end_heading_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskRequest, status_info_),
  ~0u,
  7,
  1,
  2,
  5,
  3,
  4,
  6,
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::UpdateTaskResponse, task_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::rtc::Objective)},
  { 13, -1, -1, sizeof(::carbon::rtc::ObjectiveList)},
  { 20, -1, -1, sizeof(::carbon::rtc::ListObjectivesResponse)},
  { 28, -1, -1, sizeof(::carbon::rtc::GetNextActiveObjectiveRequest)},
  { 35, -1, -1, sizeof(::carbon::rtc::GetNextActiveObjectiveResponse)},
  { 42, -1, -1, sizeof(::carbon::rtc::Task)},
  { 71, -1, -1, sizeof(::carbon::rtc::StopAutonomyTask)},
  { 77, -1, -1, sizeof(::carbon::rtc::LaserWeedTask)},
  { 89, -1, -1, sizeof(::carbon::rtc::SequenceTask)},
  { 97, -1, -1, sizeof(::carbon::rtc::ManualTask)},
  { 104, -1, -1, sizeof(::carbon::rtc::GoToAndFaceTask)},
  { 112, -1, -1, sizeof(::carbon::rtc::GoToReversiblePathTask)},
  { 120, -1, -1, sizeof(::carbon::rtc::FollowPathTask)},
  { 129, -1, -1, sizeof(::carbon::rtc::SpeedSetting)},
  { 139, -1, -1, sizeof(::carbon::rtc::SetTractorStateTask)},
  { 146, -1, -1, sizeof(::carbon::rtc::TractorState)},
  { 155, -1, -1, sizeof(::carbon::rtc::HitchState)},
  { 164, -1, -1, sizeof(::carbon::rtc::TaskList)},
  { 171, -1, -1, sizeof(::carbon::rtc::ListTasksResponse)},
  { 179, -1, -1, sizeof(::carbon::rtc::SpatialPathTolerance)},
  { 189, 207, -1, sizeof(::carbon::rtc::Job)},
  { 219, -1, -1, sizeof(::carbon::rtc::JobList)},
  { 226, -1, -1, sizeof(::carbon::rtc::ListJobsResponse)},
  { 234, -1, -1, sizeof(::carbon::rtc::WorkOrder)},
  { 246, -1, -1, sizeof(::carbon::rtc::WorkOrderList)},
  { 253, -1, -1, sizeof(::carbon::rtc::ListWorkOrdersResponse)},
  { 261, -1, -1, sizeof(::carbon::rtc::Intervention)},
  { 276, -1, -1, sizeof(::carbon::rtc::InterventionList)},
  { 283, -1, -1, sizeof(::carbon::rtc::ListInterventionsRequest)},
  { 291, -1, -1, sizeof(::carbon::rtc::ListInterventionsResponse)},
  { 299, -1, -1, sizeof(::carbon::rtc::CreateInterventionRequest)},
  { 306, -1, -1, sizeof(::carbon::rtc::CreateInterventionResponse)},
  { 313, -1, -1, sizeof(::carbon::rtc::GetActiveTaskRequest)},
  { 320, -1, -1, sizeof(::carbon::rtc::GetActiveTaskResponse)},
  { 327, -1, -1, sizeof(::carbon::rtc::GetTaskRequest)},
  { 334, -1, -1, sizeof(::carbon::rtc::GetTaskResponse)},
  { 341, 356, -1, sizeof(::carbon::rtc::UpdateTaskRequest)},
  { 365, -1, -1, sizeof(::carbon::rtc::UpdateTaskResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_Objective_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ObjectiveList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListObjectivesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetNextActiveObjectiveRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetNextActiveObjectiveResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_Task_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_StopAutonomyTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_LaserWeedTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SequenceTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ManualTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GoToAndFaceTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GoToReversiblePathTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_FollowPathTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SpeedSetting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SetTractorStateTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_TractorState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_HitchState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_TaskList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListTasksResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SpatialPathTolerance_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_Job_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_JobList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListJobsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_WorkOrder_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_WorkOrderList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListWorkOrdersResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_Intervention_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_InterventionList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListInterventionsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListInterventionsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_CreateInterventionRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_CreateInterventionResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetActiveTaskRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetActiveTaskResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetTaskRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetTaskResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_UpdateTaskRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_UpdateTaskResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frtc_2fjobs_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\024proto/rtc/jobs.proto\022\ncarbon.rtc\032\037goog"
  "le/protobuf/timestamp.proto\032\036google/prot"
  "obuf/duration.proto\032\033google/protobuf/emp"
  "ty.proto\032\034google/protobuf/struct.proto\032\023"
  "proto/geo/geo.proto\"\205\002\n\tObjective\022\n\n\002id\030"
  "\001 \001(\004\022\014\n\004name\030\002 \001(\t\022\023\n\013description\030\003 \001(\t"
  "\022\030\n\020progress_percent\030\004 \001(\005\022\020\n\010priority\030\005"
  " \001(\005\0221\n\004type\030\006 \001(\0162#.carbon.rtc.Objectiv"
  "e.ObjectiveType\022%\n\004data\030\007 \001(\0132\027.google.p"
  "rotobuf.Struct\"C\n\rObjectiveType\022\036\n\032OBJEC"
  "TIVE_TYPE_UNSPECIFIED\020\000\022\022\n\016LASER_WEED_RO"
  "W\020\001\":\n\rObjectiveList\022)\n\nobjectives\030\001 \003(\013"
  "2\025.carbon.rtc.Objective\"W\n\026ListObjective"
  "sResponse\022\022\n\npage_token\030\001 \001(\t\022)\n\nobjecti"
  "ves\030\002 \003(\0132\025.carbon.rtc.Objective\"5\n\035GetN"
  "extActiveObjectiveRequest\022\024\n\014objective_i"
  "d\030\001 \001(\004\"J\n\036GetNextActiveObjectiveRespons"
  "e\022(\n\tobjective\030\002 \001(\0132\025.carbon.rtc.Object"
  "ive\"\206\007\n\004Task\022\n\n\002id\030\001 \001(\004\022\014\n\004name\030\002 \001(\t\022."
  "\n\nstarted_at\030\003 \001(\0132\032.google.protobuf.Tim"
  "estamp\022,\n\010ended_at\030\004 \001(\0132\032.google.protob"
  "uf.Timestamp\0224\n\021expected_duration\030\005 \001(\0132"
  "\031.google.protobuf.Duration\022\023\n\013status_inf"
  "o\030\006 \001(\t\0228\n\026expected_tractor_state\030\007 \003(\0132"
  "\030.carbon.rtc.TractorState\022 \n\005state\030\010 \001(\016"
  "2\021.carbon.rtc.State\022\020\n\010priority\030\t \001(\005\022\024\n"
  "\014objective_id\030\020 \001(\004\022)\n\016start_location\030\022 "
  "\001(\0132\021.carbon.geo.Point\022\025\n\rstart_heading\030"
  "\023 \001(\001\022\'\n\014end_location\030\024 \001(\0132\021.carbon.geo"
  ".Point\022\023\n\013end_heading\030\025 \001(\001\022,\n\010sequence\030"
  "\n \001(\0132\030.carbon.rtc.SequenceTaskH\000\022(\n\006man"
  "ual\030\013 \001(\0132\026.carbon.rtc.ManualTaskH\000\0225\n\016g"
  "o_to_and_face\030\014 \001(\0132\033.carbon.rtc.GoToAnd"
  "FaceTaskH\000\0221\n\013follow_path\030\r \001(\0132\032.carbon"
  ".rtc.FollowPathTaskH\000\0228\n\rtractor_state\030\016"
  " \001(\0132\037.carbon.rtc.SetTractorStateTaskH\000\022"
  "/\n\nlaser_weed\030\017 \001(\0132\031.carbon.rtc.LaserWe"
  "edTaskH\000\0225\n\rstop_autonomy\030\021 \001(\0132\034.carbon"
  ".rtc.StopAutonomyTaskH\000\022C\n\025go_to_reversi"
  "ble_path\030\026 \001(\0132\".carbon.rtc.GoToReversib"
  "lePathTaskH\000B\016\n\014task_details\"\022\n\020StopAuto"
  "nomyTask\"\312\001\n\rLaserWeedTask\022$\n\004path\030\001 \001(\013"
  "2\026.carbon.geo.LineString\022\032\n\022path_is_reve"
  "rsible\030\002 \001(\010\022\027\n\017weeding_enabled\030\003 \001(\010\022\030\n"
  "\020thinning_enabled\030\004 \001(\010\022\016\n\006manual\030\005 \001(\010\022"
  "4\n\ntolerances\030\006 \001(\0132 .carbon.rtc.Spatial"
  "PathTolerance\"\?\n\014SequenceTask\022\037\n\005items\030\001"
  " \003(\0132\020.carbon.rtc.Task\022\016\n\006atomic\030\002 \001(\010\"\""
  "\n\nManualTask\022\024\n\014instructions\030\001 \001(\t\"D\n\017Go"
  "ToAndFaceTask\022 \n\005point\030\001 \001(\0132\021.carbon.ge"
  "o.Point\022\017\n\007heading\030\002 \001(\001\"t\n\026GoToReversib"
  "lePathTask\022$\n\004path\030\001 \001(\0132\026.carbon.geo.Li"
  "neString\0224\n\ntolerances\030\002 \001(\0132 .carbon.rt"
  "c.SpatialPathTolerance\"{\n\016FollowPathTask"
  "\022$\n\004path\030\001 \001(\0132\026.carbon.geo.LineString\022\'"
  "\n\005speed\030\002 \001(\0132\030.carbon.rtc.SpeedSetting\022"
  "\032\n\022stop_on_completion\030\003 \001(\010\"\245\001\n\014SpeedSet"
  "ting\022\026\n\014constant_mph\030\001 \001(\001H\000\022<\n\032remote_o"
  "perator_controlled\030\002 \001(\0132\026.google.protob"
  "uf.EmptyH\000\0226\n\024implement_controlled\030\003 \001(\013"
  "2\026.google.protobuf.EmptyH\000B\007\n\005speed\">\n\023S"
  "etTractorStateTask\022\'\n\005state\030\001 \003(\0132\030.carb"
  "on.rtc.TractorState\"\315\001\n\014TractorState\022-\n\004"
  "gear\030\001 \001(\0162\035.carbon.rtc.TractorState.Gea"
  "rH\000\022\'\n\005hitch\030\002 \001(\0132\026.carbon.rtc.HitchSta"
  "teH\000\"\\\n\004Gear\022\024\n\020GEAR_UNSPECIFIED\020\000\022\010\n\004PA"
  "RK\020\001\022\013\n\007REVERSE\020\002\022\013\n\007NEUTRAL\020\003\022\013\n\007FORWAR"
  "D\020\004\022\r\n\tPOWERZERO\020\005B\007\n\005state\"\251\001\n\nHitchSta"
  "te\0226\n\007command\030\001 \001(\0162#.carbon.rtc.HitchSt"
  "ate.HitchCommandH\000\022\022\n\010position\030\002 \001(\001H\000\"F"
  "\n\014HitchCommand\022\035\n\031HITCH_COMMAND_UNSPECIF"
  "IED\020\000\022\n\n\006RAISED\020\001\022\013\n\007LOWERED\020\002B\007\n\005state\""
  "+\n\010TaskList\022\037\n\005tasks\030\001 \003(\0132\020.carbon.rtc."
  "Task\"H\n\021ListTasksResponse\022\022\n\npage_token\030"
  "\001 \001(\t\022\037\n\005tasks\030\002 \003(\0132\020.carbon.rtc.Task\"l"
  "\n\024SpatialPathTolerance\022\017\n\007heading\030\001 \001(\002\022"
  "\022\n\ncrosstrack\030\002 \001(\002\022\020\n\010distance\030\003 \001(\002\022\035\n"
  "\025continuous_crosstrack\030\004 \001(\002\"\236\003\n\003Job\022\n\n\002"
  "id\030\001 \001(\004\022\014\n\004name\030\002 \001(\t\022.\n\nstarted_at\030\003 \001"
  "(\0132\032.google.protobuf.Timestamp\022,\n\010ended_"
  "at\030\004 \001(\0132\032.google.protobuf.Timestamp\022)\n\n"
  "objectives\030\005 \003(\0132\025.carbon.rtc.Objective\022"
  " \n\005state\030\006 \001(\0162\021.carbon.rtc.State\022%\n\004typ"
  "e\030\007 \001(\0162\027.carbon.rtc.Job.JobType\022\032\n\rwork"
  "_order_id\030\010 \001(\004H\000\210\001\001\022\017\n\007farm_id\030\t \001(\t\022\020\n"
  "\010field_id\030\n \001(\t\022\023\n\013customer_id\030\013 \001(\t\022\020\n\010"
  "priority\030\014 \001(\005\"3\n\007JobType\022\030\n\024JOB_TYPE_UN"
  "SPECIFIED\020\000\022\016\n\nLASER_WEED\020\001B\020\n\016_work_ord"
  "er_id\"(\n\007JobList\022\035\n\004jobs\030\001 \003(\0132\017.carbon."
  "rtc.Job\"E\n\020ListJobsResponse\022\022\n\npage_toke"
  "n\030\001 \001(\t\022\035\n\004jobs\030\002 \003(\0132\017.carbon.rtc.Job\"\262"
  "\001\n\tWorkOrder\022\n\n\002id\030\001 \001(\004\022\014\n\004name\030\002 \001(\t\0220"
  "\n\014scheduled_at\030\003 \001(\0132\032.google.protobuf.T"
  "imestamp\022\030\n\020duration_minutes\030\004 \001(\005\022\035\n\004jo"
  "bs\030\005 \003(\0132\017.carbon.rtc.Job\022 \n\005state\030\006 \001(\016"
  "2\021.carbon.rtc.State\";\n\rWorkOrderList\022*\n\013"
  "work_orders\030\001 \003(\0132\025.carbon.rtc.WorkOrder"
  "\"X\n\026ListWorkOrdersResponse\022\022\n\npage_token"
  "\030\001 \001(\t\022*\n\013work_orders\030\002 \003(\0132\025.carbon.rtc"
  ".WorkOrder\"\352\002\n\014Intervention\022\n\n\002id\030\001 \001(\004\022"
  "\017\n\007task_id\030\002 \001(\004\022\025\n\rqualification\030\003 \001(\t\022"
  "\023\n\013description\030\004 \001(\t\022 \n\005state\030\005 \001(\0162\021.ca"
  "rbon.rtc.State\022\024\n\014robot_serial\030\006 \001(\t\022\016\n\006"
  "job_id\030\007 \001(\004\0229\n\005cause\030\010 \001(\0162*.carbon.rtc"
  ".Intervention.InterventionCause\022\020\n\010prior"
  "ity\030\t \001(\005\"|\n\021InterventionCause\022\"\n\036INTERV"
  "ENTION_CAUSE_UNSPECIFIED\020\000\022\024\n\020SENSOR_TRI"
  "GGERED\020\001\022\030\n\024SAFETY_DRIVER_ACTION\020\002\022\023\n\017TR"
  "ACTOR_REQUEST\020\003\"B\n\020InterventionList\022.\n\014i"
  "ntervention\030\001 \003(\0132\030.carbon.rtc.Intervent"
  "ion\"A\n\030ListInterventionsRequest\022\021\n\tpage_"
  "size\030\001 \001(\005\022\022\n\npage_token\030\002 \001(\t\"`\n\031ListIn"
  "terventionsResponse\022\022\n\npage_token\030\001 \001(\t\022"
  "/\n\rinterventions\030\002 \003(\0132\030.carbon.rtc.Inte"
  "rvention\"K\n\031CreateInterventionRequest\022.\n"
  "\014intervention\030\001 \001(\0132\030.carbon.rtc.Interve"
  "ntion\"L\n\032CreateInterventionResponse\022.\n\014i"
  "ntervention\030\001 \001(\0132\030.carbon.rtc.Intervent"
  "ion\"C\n\024GetActiveTaskRequest\022+\n\020current_l"
  "ocation\030\002 \001(\0132\021.carbon.geo.Point\"7\n\025GetA"
  "ctiveTaskResponse\022\036\n\004task\030\001 \001(\0132\020.carbon"
  ".rtc.Task\"!\n\016GetTaskRequest\022\017\n\007task_id\030\001"
  " \001(\004\"1\n\017GetTaskResponse\022\036\n\004task\030\001 \001(\0132\020."
  "carbon.rtc.Task\"\335\003\n\021UpdateTaskRequest\022\017\n"
  "\007task_id\030\001 \001(\004\022%\n\005state\030\002 \001(\0162\021.carbon.r"
  "tc.StateH\000\210\001\001\0223\n\nstarted_at\030\003 \001(\0132\032.goog"
  "le.protobuf.TimestampH\001\210\001\001\022.\n\016start_loca"
  "tion\030\004 \001(\0132\021.carbon.geo.PointH\002\210\001\001\022\032\n\rst"
  "art_heading\030\005 \001(\001H\003\210\001\001\0221\n\010ended_at\030\006 \001(\013"
  "2\032.google.protobuf.TimestampH\004\210\001\001\022,\n\014end"
  "_location\030\007 \001(\0132\021.carbon.geo.PointH\005\210\001\001\022"
  "\030\n\013end_heading\030\010 \001(\001H\006\210\001\001\022\030\n\013status_info"
  "\030\t \001(\tH\007\210\001\001B\010\n\006_stateB\r\n\013_started_atB\021\n\017"
  "_start_locationB\020\n\016_start_headingB\013\n\t_en"
  "ded_atB\017\n\r_end_locationB\016\n\014_end_headingB"
  "\016\n\014_status_info\"4\n\022UpdateTaskResponse\022\036\n"
  "\004task\030\001 \001(\0132\020.carbon.rtc.Task*\230\001\n\005State\022"
  "\025\n\021STATE_UNSPECIFIED\020\000\022\013\n\007PENDING\020\001\022\t\n\005R"
  "EADY\020\002\022\017\n\013IN_PROGRESS\020\003\022\r\n\tCOMPLETED\020\004\022\r"
  "\n\tCANCELLED\020\005\022\n\n\006PAUSED\020\006\022\n\n\006FAILED\020\007\022\020\n"
  "\014ACKNOWLEDGED\020\010\022\007\n\003NEW\020\t2\311\003\n\nJobService\022"
  "c\n\022CreateIntervention\022%.carbon.rtc.Creat"
  "eInterventionRequest\032&.carbon.rtc.Create"
  "InterventionResponse\022T\n\rGetActiveTask\022 ."
  "carbon.rtc.GetActiveTaskRequest\032!.carbon"
  ".rtc.GetActiveTaskResponse\022B\n\007GetTask\022\032."
  "carbon.rtc.GetTaskRequest\032\033.carbon.rtc.G"
  "etTaskResponse\022o\n\026GetNextActiveObjective"
  "\022).carbon.rtc.GetNextActiveObjectiveRequ"
  "est\032*.carbon.rtc.GetNextActiveObjectiveR"
  "esponse\022K\n\nUpdateTask\022\035.carbon.rtc.Updat"
  "eTaskRequest\032\036.carbon.rtc.UpdateTaskResp"
  "onseB\013Z\tproto/rtcb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frtc_2fjobs_2eproto_deps[5] = {
  &::descriptor_table_google_2fprotobuf_2fduration_2eproto,
  &::descriptor_table_google_2fprotobuf_2fempty_2eproto,
  &::descriptor_table_google_2fprotobuf_2fstruct_2eproto,
  &::descriptor_table_google_2fprotobuf_2ftimestamp_2eproto,
  &::descriptor_table_proto_2fgeo_2fgeo_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frtc_2fjobs_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2fjobs_2eproto = {
  false, false, 6105, descriptor_table_protodef_proto_2frtc_2fjobs_2eproto, "proto/rtc/jobs.proto", 
  &descriptor_table_proto_2frtc_2fjobs_2eproto_once, descriptor_table_proto_2frtc_2fjobs_2eproto_deps, 5, 38,
  schemas, file_default_instances, TableStruct_proto_2frtc_2fjobs_2eproto::offsets,
  file_level_metadata_proto_2frtc_2fjobs_2eproto, file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto, file_level_service_descriptors_proto_2frtc_2fjobs_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2frtc_2fjobs_2eproto_getter() {
  return &descriptor_table_proto_2frtc_2fjobs_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2frtc_2fjobs_2eproto(&descriptor_table_proto_2frtc_2fjobs_2eproto);
namespace carbon {
namespace rtc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Objective_ObjectiveType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[0];
}
bool Objective_ObjectiveType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Objective_ObjectiveType Objective::OBJECTIVE_TYPE_UNSPECIFIED;
constexpr Objective_ObjectiveType Objective::LASER_WEED_ROW;
constexpr Objective_ObjectiveType Objective::ObjectiveType_MIN;
constexpr Objective_ObjectiveType Objective::ObjectiveType_MAX;
constexpr int Objective::ObjectiveType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TractorState_Gear_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[1];
}
bool TractorState_Gear_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr TractorState_Gear TractorState::GEAR_UNSPECIFIED;
constexpr TractorState_Gear TractorState::PARK;
constexpr TractorState_Gear TractorState::REVERSE;
constexpr TractorState_Gear TractorState::NEUTRAL;
constexpr TractorState_Gear TractorState::FORWARD;
constexpr TractorState_Gear TractorState::POWERZERO;
constexpr TractorState_Gear TractorState::Gear_MIN;
constexpr TractorState_Gear TractorState::Gear_MAX;
constexpr int TractorState::Gear_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HitchState_HitchCommand_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[2];
}
bool HitchState_HitchCommand_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr HitchState_HitchCommand HitchState::HITCH_COMMAND_UNSPECIFIED;
constexpr HitchState_HitchCommand HitchState::RAISED;
constexpr HitchState_HitchCommand HitchState::LOWERED;
constexpr HitchState_HitchCommand HitchState::HitchCommand_MIN;
constexpr HitchState_HitchCommand HitchState::HitchCommand_MAX;
constexpr int HitchState::HitchCommand_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Job_JobType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[3];
}
bool Job_JobType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Job_JobType Job::JOB_TYPE_UNSPECIFIED;
constexpr Job_JobType Job::LASER_WEED;
constexpr Job_JobType Job::JobType_MIN;
constexpr Job_JobType Job::JobType_MAX;
constexpr int Job::JobType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Intervention_InterventionCause_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[4];
}
bool Intervention_InterventionCause_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Intervention_InterventionCause Intervention::INTERVENTION_CAUSE_UNSPECIFIED;
constexpr Intervention_InterventionCause Intervention::SENSOR_TRIGGERED;
constexpr Intervention_InterventionCause Intervention::SAFETY_DRIVER_ACTION;
constexpr Intervention_InterventionCause Intervention::TRACTOR_REQUEST;
constexpr Intervention_InterventionCause Intervention::InterventionCause_MIN;
constexpr Intervention_InterventionCause Intervention::InterventionCause_MAX;
constexpr int Intervention::InterventionCause_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* State_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fjobs_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fjobs_2eproto[5];
}
bool State_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class Objective::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Struct& data(const Objective* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Struct&
Objective::_Internal::data(const Objective* msg) {
  return *msg->data_;
}
void Objective::clear_data() {
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
}
Objective::Objective(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.Objective)
}
Objective::Objective(const Objective& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_data()) {
    data_ = new ::PROTOBUF_NAMESPACE_ID::Struct(*from.data_);
  } else {
    data_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&id_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.Objective)
}

inline void Objective::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&data_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&data_)) + sizeof(type_));
}

Objective::~Objective() {
  // @@protoc_insertion_point(destructor:carbon.rtc.Objective)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Objective::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete data_;
}

void Objective::ArenaDtor(void* object) {
  Objective* _this = reinterpret_cast< Objective* >(object);
  (void)_this;
}
void Objective::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Objective::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Objective::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.Objective)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  description_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&id_)) + sizeof(type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Objective::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Objective.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Objective.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 progress_percent = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          progress_percent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.Objective.ObjectiveType type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::rtc::Objective_ObjectiveType>(val));
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Struct data = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Objective::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.Objective)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Objective.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Objective.description");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_description(), target);
  }

  // int32 progress_percent = 4;
  if (this->_internal_progress_percent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_progress_percent(), target);
  }

  // int32 priority = 5;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_priority(), target);
  }

  // .carbon.rtc.Objective.ObjectiveType type = 6;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_type(), target);
  }

  // .google.protobuf.Struct data = 7;
  if (this->_internal_has_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::data(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.Objective)
  return target;
}

size_t Objective::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.Objective)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // .google.protobuf.Struct data = 7;
  if (this->_internal_has_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *data_);
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  // int32 progress_percent = 4;
  if (this->_internal_progress_percent() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_progress_percent());
  }

  // int32 priority = 5;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  // .carbon.rtc.Objective.ObjectiveType type = 6;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Objective::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Objective::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Objective::GetClassData() const { return &_class_data_; }

void Objective::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Objective *>(to)->MergeFrom(
      static_cast<const Objective &>(from));
}


void Objective::MergeFrom(const Objective& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.Objective)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (from._internal_has_data()) {
    _internal_mutable_data()->::PROTOBUF_NAMESPACE_ID::Struct::MergeFrom(from._internal_data());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_progress_percent() != 0) {
    _internal_set_progress_percent(from._internal_progress_percent());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Objective::CopyFrom(const Objective& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.Objective)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Objective::IsInitialized() const {
  return true;
}

void Objective::InternalSwap(Objective* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Objective, type_)
      + sizeof(Objective::type_)
      - PROTOBUF_FIELD_OFFSET(Objective, data_)>(
          reinterpret_cast<char*>(&data_),
          reinterpret_cast<char*>(&other->data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Objective::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[0]);
}

// ===================================================================

class ObjectiveList::_Internal {
 public:
};

ObjectiveList::ObjectiveList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  objectives_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ObjectiveList)
}
ObjectiveList::ObjectiveList(const ObjectiveList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      objectives_(from.objectives_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ObjectiveList)
}

inline void ObjectiveList::SharedCtor() {
}

ObjectiveList::~ObjectiveList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ObjectiveList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ObjectiveList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ObjectiveList::ArenaDtor(void* object) {
  ObjectiveList* _this = reinterpret_cast< ObjectiveList* >(object);
  (void)_this;
}
void ObjectiveList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ObjectiveList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ObjectiveList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ObjectiveList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  objectives_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ObjectiveList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.Objective objectives = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objectives(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ObjectiveList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ObjectiveList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.Objective objectives = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_objectives_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_objectives(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ObjectiveList)
  return target;
}

size_t ObjectiveList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ObjectiveList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Objective objectives = 1;
  total_size += 1UL * this->_internal_objectives_size();
  for (const auto& msg : this->objectives_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ObjectiveList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ObjectiveList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ObjectiveList::GetClassData() const { return &_class_data_; }

void ObjectiveList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ObjectiveList *>(to)->MergeFrom(
      static_cast<const ObjectiveList &>(from));
}


void ObjectiveList::MergeFrom(const ObjectiveList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ObjectiveList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  objectives_.MergeFrom(from.objectives_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ObjectiveList::CopyFrom(const ObjectiveList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ObjectiveList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ObjectiveList::IsInitialized() const {
  return true;
}

void ObjectiveList::InternalSwap(ObjectiveList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  objectives_.InternalSwap(&other->objectives_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ObjectiveList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[1]);
}

// ===================================================================

class ListObjectivesResponse::_Internal {
 public:
};

ListObjectivesResponse::ListObjectivesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  objectives_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListObjectivesResponse)
}
ListObjectivesResponse::ListObjectivesResponse(const ListObjectivesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      objectives_(from.objectives_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListObjectivesResponse)
}

inline void ListObjectivesResponse::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListObjectivesResponse::~ListObjectivesResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListObjectivesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListObjectivesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListObjectivesResponse::ArenaDtor(void* object) {
  ListObjectivesResponse* _this = reinterpret_cast< ListObjectivesResponse* >(object);
  (void)_this;
}
void ListObjectivesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListObjectivesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListObjectivesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListObjectivesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  objectives_.Clear();
  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListObjectivesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListObjectivesResponse.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Objective objectives = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objectives(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListObjectivesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListObjectivesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListObjectivesResponse.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  // repeated .carbon.rtc.Objective objectives = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_objectives_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_objectives(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListObjectivesResponse)
  return target;
}

size_t ListObjectivesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListObjectivesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Objective objectives = 2;
  total_size += 1UL * this->_internal_objectives_size();
  for (const auto& msg : this->objectives_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListObjectivesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListObjectivesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListObjectivesResponse::GetClassData() const { return &_class_data_; }

void ListObjectivesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListObjectivesResponse *>(to)->MergeFrom(
      static_cast<const ListObjectivesResponse &>(from));
}


void ListObjectivesResponse::MergeFrom(const ListObjectivesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListObjectivesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  objectives_.MergeFrom(from.objectives_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListObjectivesResponse::CopyFrom(const ListObjectivesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListObjectivesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListObjectivesResponse::IsInitialized() const {
  return true;
}

void ListObjectivesResponse::InternalSwap(ListObjectivesResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  objectives_.InternalSwap(&other->objectives_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListObjectivesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[2]);
}

// ===================================================================

class GetNextActiveObjectiveRequest::_Internal {
 public:
};

GetNextActiveObjectiveRequest::GetNextActiveObjectiveRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetNextActiveObjectiveRequest)
}
GetNextActiveObjectiveRequest::GetNextActiveObjectiveRequest(const GetNextActiveObjectiveRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  objective_id_ = from.objective_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetNextActiveObjectiveRequest)
}

inline void GetNextActiveObjectiveRequest::SharedCtor() {
objective_id_ = uint64_t{0u};
}

GetNextActiveObjectiveRequest::~GetNextActiveObjectiveRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetNextActiveObjectiveRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveObjectiveRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetNextActiveObjectiveRequest::ArenaDtor(void* object) {
  GetNextActiveObjectiveRequest* _this = reinterpret_cast< GetNextActiveObjectiveRequest* >(object);
  (void)_this;
}
void GetNextActiveObjectiveRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveObjectiveRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveObjectiveRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetNextActiveObjectiveRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  objective_id_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveObjectiveRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 objective_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          objective_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveObjectiveRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetNextActiveObjectiveRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 objective_id = 1;
  if (this->_internal_objective_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_objective_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetNextActiveObjectiveRequest)
  return target;
}

size_t GetNextActiveObjectiveRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetNextActiveObjectiveRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 objective_id = 1;
  if (this->_internal_objective_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_objective_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveObjectiveRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveObjectiveRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveObjectiveRequest::GetClassData() const { return &_class_data_; }

void GetNextActiveObjectiveRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveObjectiveRequest *>(to)->MergeFrom(
      static_cast<const GetNextActiveObjectiveRequest &>(from));
}


void GetNextActiveObjectiveRequest::MergeFrom(const GetNextActiveObjectiveRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetNextActiveObjectiveRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_objective_id() != 0) {
    _internal_set_objective_id(from._internal_objective_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveObjectiveRequest::CopyFrom(const GetNextActiveObjectiveRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetNextActiveObjectiveRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveObjectiveRequest::IsInitialized() const {
  return true;
}

void GetNextActiveObjectiveRequest::InternalSwap(GetNextActiveObjectiveRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(objective_id_, other->objective_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveObjectiveRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[3]);
}

// ===================================================================

class GetNextActiveObjectiveResponse::_Internal {
 public:
  static const ::carbon::rtc::Objective& objective(const GetNextActiveObjectiveResponse* msg);
};

const ::carbon::rtc::Objective&
GetNextActiveObjectiveResponse::_Internal::objective(const GetNextActiveObjectiveResponse* msg) {
  return *msg->objective_;
}
GetNextActiveObjectiveResponse::GetNextActiveObjectiveResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetNextActiveObjectiveResponse)
}
GetNextActiveObjectiveResponse::GetNextActiveObjectiveResponse(const GetNextActiveObjectiveResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_objective()) {
    objective_ = new ::carbon::rtc::Objective(*from.objective_);
  } else {
    objective_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetNextActiveObjectiveResponse)
}

inline void GetNextActiveObjectiveResponse::SharedCtor() {
objective_ = nullptr;
}

GetNextActiveObjectiveResponse::~GetNextActiveObjectiveResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetNextActiveObjectiveResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveObjectiveResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete objective_;
}

void GetNextActiveObjectiveResponse::ArenaDtor(void* object) {
  GetNextActiveObjectiveResponse* _this = reinterpret_cast< GetNextActiveObjectiveResponse* >(object);
  (void)_this;
}
void GetNextActiveObjectiveResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveObjectiveResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveObjectiveResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetNextActiveObjectiveResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && objective_ != nullptr) {
    delete objective_;
  }
  objective_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveObjectiveResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Objective objective = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_objective(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveObjectiveResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetNextActiveObjectiveResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Objective objective = 2;
  if (this->_internal_has_objective()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::objective(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetNextActiveObjectiveResponse)
  return target;
}

size_t GetNextActiveObjectiveResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetNextActiveObjectiveResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Objective objective = 2;
  if (this->_internal_has_objective()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *objective_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveObjectiveResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveObjectiveResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveObjectiveResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveObjectiveResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveObjectiveResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveObjectiveResponse &>(from));
}


void GetNextActiveObjectiveResponse::MergeFrom(const GetNextActiveObjectiveResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetNextActiveObjectiveResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_objective()) {
    _internal_mutable_objective()->::carbon::rtc::Objective::MergeFrom(from._internal_objective());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveObjectiveResponse::CopyFrom(const GetNextActiveObjectiveResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetNextActiveObjectiveResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveObjectiveResponse::IsInitialized() const {
  return true;
}

void GetNextActiveObjectiveResponse::InternalSwap(GetNextActiveObjectiveResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(objective_, other->objective_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveObjectiveResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[4]);
}

// ===================================================================

class Task::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& started_at(const Task* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& ended_at(const Task* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Duration& expected_duration(const Task* msg);
  static const ::carbon::geo::Point& start_location(const Task* msg);
  static const ::carbon::geo::Point& end_location(const Task* msg);
  static const ::carbon::rtc::SequenceTask& sequence(const Task* msg);
  static const ::carbon::rtc::ManualTask& manual(const Task* msg);
  static const ::carbon::rtc::GoToAndFaceTask& go_to_and_face(const Task* msg);
  static const ::carbon::rtc::FollowPathTask& follow_path(const Task* msg);
  static const ::carbon::rtc::SetTractorStateTask& tractor_state(const Task* msg);
  static const ::carbon::rtc::LaserWeedTask& laser_weed(const Task* msg);
  static const ::carbon::rtc::StopAutonomyTask& stop_autonomy(const Task* msg);
  static const ::carbon::rtc::GoToReversiblePathTask& go_to_reversible_path(const Task* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
Task::_Internal::started_at(const Task* msg) {
  return *msg->started_at_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
Task::_Internal::ended_at(const Task* msg) {
  return *msg->ended_at_;
}
const ::PROTOBUF_NAMESPACE_ID::Duration&
Task::_Internal::expected_duration(const Task* msg) {
  return *msg->expected_duration_;
}
const ::carbon::geo::Point&
Task::_Internal::start_location(const Task* msg) {
  return *msg->start_location_;
}
const ::carbon::geo::Point&
Task::_Internal::end_location(const Task* msg) {
  return *msg->end_location_;
}
const ::carbon::rtc::SequenceTask&
Task::_Internal::sequence(const Task* msg) {
  return *msg->task_details_.sequence_;
}
const ::carbon::rtc::ManualTask&
Task::_Internal::manual(const Task* msg) {
  return *msg->task_details_.manual_;
}
const ::carbon::rtc::GoToAndFaceTask&
Task::_Internal::go_to_and_face(const Task* msg) {
  return *msg->task_details_.go_to_and_face_;
}
const ::carbon::rtc::FollowPathTask&
Task::_Internal::follow_path(const Task* msg) {
  return *msg->task_details_.follow_path_;
}
const ::carbon::rtc::SetTractorStateTask&
Task::_Internal::tractor_state(const Task* msg) {
  return *msg->task_details_.tractor_state_;
}
const ::carbon::rtc::LaserWeedTask&
Task::_Internal::laser_weed(const Task* msg) {
  return *msg->task_details_.laser_weed_;
}
const ::carbon::rtc::StopAutonomyTask&
Task::_Internal::stop_autonomy(const Task* msg) {
  return *msg->task_details_.stop_autonomy_;
}
const ::carbon::rtc::GoToReversiblePathTask&
Task::_Internal::go_to_reversible_path(const Task* msg) {
  return *msg->task_details_.go_to_reversible_path_;
}
void Task::clear_started_at() {
  if (GetArenaForAllocation() == nullptr && started_at_ != nullptr) {
    delete started_at_;
  }
  started_at_ = nullptr;
}
void Task::clear_ended_at() {
  if (GetArenaForAllocation() == nullptr && ended_at_ != nullptr) {
    delete ended_at_;
  }
  ended_at_ = nullptr;
}
void Task::clear_expected_duration() {
  if (GetArenaForAllocation() == nullptr && expected_duration_ != nullptr) {
    delete expected_duration_;
  }
  expected_duration_ = nullptr;
}
void Task::clear_start_location() {
  if (GetArenaForAllocation() == nullptr && start_location_ != nullptr) {
    delete start_location_;
  }
  start_location_ = nullptr;
}
void Task::clear_end_location() {
  if (GetArenaForAllocation() == nullptr && end_location_ != nullptr) {
    delete end_location_;
  }
  end_location_ = nullptr;
}
void Task::set_allocated_sequence(::carbon::rtc::SequenceTask* sequence) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (sequence) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::SequenceTask>::GetOwningArena(sequence);
    if (message_arena != submessage_arena) {
      sequence = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sequence, submessage_arena);
    }
    set_has_sequence();
    task_details_.sequence_ = sequence;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.sequence)
}
void Task::set_allocated_manual(::carbon::rtc::ManualTask* manual) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (manual) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::ManualTask>::GetOwningArena(manual);
    if (message_arena != submessage_arena) {
      manual = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, manual, submessage_arena);
    }
    set_has_manual();
    task_details_.manual_ = manual;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.manual)
}
void Task::set_allocated_go_to_and_face(::carbon::rtc::GoToAndFaceTask* go_to_and_face) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (go_to_and_face) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::GoToAndFaceTask>::GetOwningArena(go_to_and_face);
    if (message_arena != submessage_arena) {
      go_to_and_face = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, go_to_and_face, submessage_arena);
    }
    set_has_go_to_and_face();
    task_details_.go_to_and_face_ = go_to_and_face;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.go_to_and_face)
}
void Task::set_allocated_follow_path(::carbon::rtc::FollowPathTask* follow_path) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (follow_path) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::FollowPathTask>::GetOwningArena(follow_path);
    if (message_arena != submessage_arena) {
      follow_path = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, follow_path, submessage_arena);
    }
    set_has_follow_path();
    task_details_.follow_path_ = follow_path;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.follow_path)
}
void Task::set_allocated_tractor_state(::carbon::rtc::SetTractorStateTask* tractor_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (tractor_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::SetTractorStateTask>::GetOwningArena(tractor_state);
    if (message_arena != submessage_arena) {
      tractor_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tractor_state, submessage_arena);
    }
    set_has_tractor_state();
    task_details_.tractor_state_ = tractor_state;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.tractor_state)
}
void Task::set_allocated_laser_weed(::carbon::rtc::LaserWeedTask* laser_weed) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (laser_weed) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::LaserWeedTask>::GetOwningArena(laser_weed);
    if (message_arena != submessage_arena) {
      laser_weed = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_weed, submessage_arena);
    }
    set_has_laser_weed();
    task_details_.laser_weed_ = laser_weed;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.laser_weed)
}
void Task::set_allocated_stop_autonomy(::carbon::rtc::StopAutonomyTask* stop_autonomy) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (stop_autonomy) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::StopAutonomyTask>::GetOwningArena(stop_autonomy);
    if (message_arena != submessage_arena) {
      stop_autonomy = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stop_autonomy, submessage_arena);
    }
    set_has_stop_autonomy();
    task_details_.stop_autonomy_ = stop_autonomy;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.stop_autonomy)
}
void Task::set_allocated_go_to_reversible_path(::carbon::rtc::GoToReversiblePathTask* go_to_reversible_path) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (go_to_reversible_path) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::GoToReversiblePathTask>::GetOwningArena(go_to_reversible_path);
    if (message_arena != submessage_arena) {
      go_to_reversible_path = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, go_to_reversible_path, submessage_arena);
    }
    set_has_go_to_reversible_path();
    task_details_.go_to_reversible_path_ = go_to_reversible_path;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.Task.go_to_reversible_path)
}
Task::Task(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  expected_tractor_state_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.Task)
}
Task::Task(const Task& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      expected_tractor_state_(from.expected_tractor_state_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  status_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status_info().empty()) {
    status_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_info(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_started_at()) {
    started_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.started_at_);
  } else {
    started_at_ = nullptr;
  }
  if (from._internal_has_ended_at()) {
    ended_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.ended_at_);
  } else {
    ended_at_ = nullptr;
  }
  if (from._internal_has_expected_duration()) {
    expected_duration_ = new ::PROTOBUF_NAMESPACE_ID::Duration(*from.expected_duration_);
  } else {
    expected_duration_ = nullptr;
  }
  if (from._internal_has_start_location()) {
    start_location_ = new ::carbon::geo::Point(*from.start_location_);
  } else {
    start_location_ = nullptr;
  }
  if (from._internal_has_end_location()) {
    end_location_ = new ::carbon::geo::Point(*from.end_location_);
  } else {
    end_location_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_heading_) -
    reinterpret_cast<char*>(&id_)) + sizeof(end_heading_));
  clear_has_task_details();
  switch (from.task_details_case()) {
    case kSequence: {
      _internal_mutable_sequence()->::carbon::rtc::SequenceTask::MergeFrom(from._internal_sequence());
      break;
    }
    case kManual: {
      _internal_mutable_manual()->::carbon::rtc::ManualTask::MergeFrom(from._internal_manual());
      break;
    }
    case kGoToAndFace: {
      _internal_mutable_go_to_and_face()->::carbon::rtc::GoToAndFaceTask::MergeFrom(from._internal_go_to_and_face());
      break;
    }
    case kFollowPath: {
      _internal_mutable_follow_path()->::carbon::rtc::FollowPathTask::MergeFrom(from._internal_follow_path());
      break;
    }
    case kTractorState: {
      _internal_mutable_tractor_state()->::carbon::rtc::SetTractorStateTask::MergeFrom(from._internal_tractor_state());
      break;
    }
    case kLaserWeed: {
      _internal_mutable_laser_weed()->::carbon::rtc::LaserWeedTask::MergeFrom(from._internal_laser_weed());
      break;
    }
    case kStopAutonomy: {
      _internal_mutable_stop_autonomy()->::carbon::rtc::StopAutonomyTask::MergeFrom(from._internal_stop_autonomy());
      break;
    }
    case kGoToReversiblePath: {
      _internal_mutable_go_to_reversible_path()->::carbon::rtc::GoToReversiblePathTask::MergeFrom(from._internal_go_to_reversible_path());
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.Task)
}

inline void Task::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&started_at_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&end_heading_) -
    reinterpret_cast<char*>(&started_at_)) + sizeof(end_heading_));
clear_has_task_details();
}

Task::~Task() {
  // @@protoc_insertion_point(destructor:carbon.rtc.Task)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Task::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  status_info_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete started_at_;
  if (this != internal_default_instance()) delete ended_at_;
  if (this != internal_default_instance()) delete expected_duration_;
  if (this != internal_default_instance()) delete start_location_;
  if (this != internal_default_instance()) delete end_location_;
  if (has_task_details()) {
    clear_task_details();
  }
}

void Task::ArenaDtor(void* object) {
  Task* _this = reinterpret_cast< Task* >(object);
  (void)_this;
}
void Task::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Task::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Task::clear_task_details() {
// @@protoc_insertion_point(one_of_clear_start:carbon.rtc.Task)
  switch (task_details_case()) {
    case kSequence: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.sequence_;
      }
      break;
    }
    case kManual: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.manual_;
      }
      break;
    }
    case kGoToAndFace: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.go_to_and_face_;
      }
      break;
    }
    case kFollowPath: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.follow_path_;
      }
      break;
    }
    case kTractorState: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.tractor_state_;
      }
      break;
    }
    case kLaserWeed: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.laser_weed_;
      }
      break;
    }
    case kStopAutonomy: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.stop_autonomy_;
      }
      break;
    }
    case kGoToReversiblePath: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.go_to_reversible_path_;
      }
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TASK_DETAILS_NOT_SET;
}


void Task::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.Task)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  expected_tractor_state_.Clear();
  name_.ClearToEmpty();
  status_info_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && started_at_ != nullptr) {
    delete started_at_;
  }
  started_at_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ended_at_ != nullptr) {
    delete ended_at_;
  }
  ended_at_ = nullptr;
  if (GetArenaForAllocation() == nullptr && expected_duration_ != nullptr) {
    delete expected_duration_;
  }
  expected_duration_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_location_ != nullptr) {
    delete start_location_;
  }
  start_location_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_location_ != nullptr) {
    delete end_location_;
  }
  end_location_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_heading_) -
      reinterpret_cast<char*>(&id_)) + sizeof(end_heading_));
  clear_task_details();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Task::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Task.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp started_at = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_started_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp ended_at = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_ended_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Duration expected_duration = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_expected_duration(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string status_info = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_status_info();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Task.status_info"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.TractorState expected_tractor_state = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_expected_tractor_state(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.State state = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::State>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.SequenceTask sequence = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_sequence(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.ManualTask manual = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_manual(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.GoToAndFaceTask go_to_and_face = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ctx->ParseMessage(_internal_mutable_go_to_and_face(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.FollowPathTask follow_path = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr = ctx->ParseMessage(_internal_mutable_follow_path(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.SetTractorStateTask tractor_state = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_tractor_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.LaserWeedTask laser_weed = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_weed(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 objective_id = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          objective_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.StopAutonomyTask stop_autonomy = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_stop_autonomy(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Point start_location = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double start_heading = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 153)) {
          start_heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Point end_location = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double end_heading = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 169)) {
          end_heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.GoToReversiblePathTask go_to_reversible_path = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          ptr = ctx->ParseMessage(_internal_mutable_go_to_reversible_path(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Task::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.Task)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Task.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // .google.protobuf.Timestamp started_at = 3;
  if (this->_internal_has_started_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::started_at(this), target, stream);
  }

  // .google.protobuf.Timestamp ended_at = 4;
  if (this->_internal_has_ended_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::ended_at(this), target, stream);
  }

  // .google.protobuf.Duration expected_duration = 5;
  if (this->_internal_has_expected_duration()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::expected_duration(this), target, stream);
  }

  // string status_info = 6;
  if (!this->_internal_status_info().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_info().data(), static_cast<int>(this->_internal_status_info().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Task.status_info");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_status_info(), target);
  }

  // repeated .carbon.rtc.TractorState expected_tractor_state = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_expected_tractor_state_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_expected_tractor_state(i), target, stream);
  }

  // .carbon.rtc.State state = 8;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_state(), target);
  }

  // int32 priority = 9;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(9, this->_internal_priority(), target);
  }

  // .carbon.rtc.SequenceTask sequence = 10;
  if (_internal_has_sequence()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::sequence(this), target, stream);
  }

  // .carbon.rtc.ManualTask manual = 11;
  if (_internal_has_manual()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::manual(this), target, stream);
  }

  // .carbon.rtc.GoToAndFaceTask go_to_and_face = 12;
  if (_internal_has_go_to_and_face()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        12, _Internal::go_to_and_face(this), target, stream);
  }

  // .carbon.rtc.FollowPathTask follow_path = 13;
  if (_internal_has_follow_path()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        13, _Internal::follow_path(this), target, stream);
  }

  // .carbon.rtc.SetTractorStateTask tractor_state = 14;
  if (_internal_has_tractor_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::tractor_state(this), target, stream);
  }

  // .carbon.rtc.LaserWeedTask laser_weed = 15;
  if (_internal_has_laser_weed()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        15, _Internal::laser_weed(this), target, stream);
  }

  // uint64 objective_id = 16;
  if (this->_internal_objective_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(16, this->_internal_objective_id(), target);
  }

  // .carbon.rtc.StopAutonomyTask stop_autonomy = 17;
  if (_internal_has_stop_autonomy()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        17, _Internal::stop_autonomy(this), target, stream);
  }

  // .carbon.geo.Point start_location = 18;
  if (this->_internal_has_start_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        18, _Internal::start_location(this), target, stream);
  }

  // double start_heading = 19;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_start_heading = this->_internal_start_heading();
  uint64_t raw_start_heading;
  memcpy(&raw_start_heading, &tmp_start_heading, sizeof(tmp_start_heading));
  if (raw_start_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(19, this->_internal_start_heading(), target);
  }

  // .carbon.geo.Point end_location = 20;
  if (this->_internal_has_end_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        20, _Internal::end_location(this), target, stream);
  }

  // double end_heading = 21;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_end_heading = this->_internal_end_heading();
  uint64_t raw_end_heading;
  memcpy(&raw_end_heading, &tmp_end_heading, sizeof(tmp_end_heading));
  if (raw_end_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(21, this->_internal_end_heading(), target);
  }

  // .carbon.rtc.GoToReversiblePathTask go_to_reversible_path = 22;
  if (_internal_has_go_to_reversible_path()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        22, _Internal::go_to_reversible_path(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.Task)
  return target;
}

size_t Task::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.Task)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.TractorState expected_tractor_state = 7;
  total_size += 1UL * this->_internal_expected_tractor_state_size();
  for (const auto& msg : this->expected_tractor_state_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string status_info = 6;
  if (!this->_internal_status_info().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status_info());
  }

  // .google.protobuf.Timestamp started_at = 3;
  if (this->_internal_has_started_at()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *started_at_);
  }

  // .google.protobuf.Timestamp ended_at = 4;
  if (this->_internal_has_ended_at()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ended_at_);
  }

  // .google.protobuf.Duration expected_duration = 5;
  if (this->_internal_has_expected_duration()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *expected_duration_);
  }

  // .carbon.geo.Point start_location = 18;
  if (this->_internal_has_start_location()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_location_);
  }

  // .carbon.geo.Point end_location = 20;
  if (this->_internal_has_end_location()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_location_);
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  // .carbon.rtc.State state = 8;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  // int32 priority = 9;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  // uint64 objective_id = 16;
  if (this->_internal_objective_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_objective_id());
  }

  // double start_heading = 19;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_start_heading = this->_internal_start_heading();
  uint64_t raw_start_heading;
  memcpy(&raw_start_heading, &tmp_start_heading, sizeof(tmp_start_heading));
  if (raw_start_heading != 0) {
    total_size += 2 + 8;
  }

  // double end_heading = 21;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_end_heading = this->_internal_end_heading();
  uint64_t raw_end_heading;
  memcpy(&raw_end_heading, &tmp_end_heading, sizeof(tmp_end_heading));
  if (raw_end_heading != 0) {
    total_size += 2 + 8;
  }

  switch (task_details_case()) {
    // .carbon.rtc.SequenceTask sequence = 10;
    case kSequence: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.sequence_);
      break;
    }
    // .carbon.rtc.ManualTask manual = 11;
    case kManual: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.manual_);
      break;
    }
    // .carbon.rtc.GoToAndFaceTask go_to_and_face = 12;
    case kGoToAndFace: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.go_to_and_face_);
      break;
    }
    // .carbon.rtc.FollowPathTask follow_path = 13;
    case kFollowPath: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.follow_path_);
      break;
    }
    // .carbon.rtc.SetTractorStateTask tractor_state = 14;
    case kTractorState: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.tractor_state_);
      break;
    }
    // .carbon.rtc.LaserWeedTask laser_weed = 15;
    case kLaserWeed: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.laser_weed_);
      break;
    }
    // .carbon.rtc.StopAutonomyTask stop_autonomy = 17;
    case kStopAutonomy: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.stop_autonomy_);
      break;
    }
    // .carbon.rtc.GoToReversiblePathTask go_to_reversible_path = 22;
    case kGoToReversiblePath: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.go_to_reversible_path_);
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Task::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Task::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Task::GetClassData() const { return &_class_data_; }

void Task::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Task *>(to)->MergeFrom(
      static_cast<const Task &>(from));
}


void Task::MergeFrom(const Task& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.Task)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  expected_tractor_state_.MergeFrom(from.expected_tractor_state_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_status_info().empty()) {
    _internal_set_status_info(from._internal_status_info());
  }
  if (from._internal_has_started_at()) {
    _internal_mutable_started_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_started_at());
  }
  if (from._internal_has_ended_at()) {
    _internal_mutable_ended_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_ended_at());
  }
  if (from._internal_has_expected_duration()) {
    _internal_mutable_expected_duration()->::PROTOBUF_NAMESPACE_ID::Duration::MergeFrom(from._internal_expected_duration());
  }
  if (from._internal_has_start_location()) {
    _internal_mutable_start_location()->::carbon::geo::Point::MergeFrom(from._internal_start_location());
  }
  if (from._internal_has_end_location()) {
    _internal_mutable_end_location()->::carbon::geo::Point::MergeFrom(from._internal_end_location());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  if (from._internal_objective_id() != 0) {
    _internal_set_objective_id(from._internal_objective_id());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_start_heading = from._internal_start_heading();
  uint64_t raw_start_heading;
  memcpy(&raw_start_heading, &tmp_start_heading, sizeof(tmp_start_heading));
  if (raw_start_heading != 0) {
    _internal_set_start_heading(from._internal_start_heading());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_end_heading = from._internal_end_heading();
  uint64_t raw_end_heading;
  memcpy(&raw_end_heading, &tmp_end_heading, sizeof(tmp_end_heading));
  if (raw_end_heading != 0) {
    _internal_set_end_heading(from._internal_end_heading());
  }
  switch (from.task_details_case()) {
    case kSequence: {
      _internal_mutable_sequence()->::carbon::rtc::SequenceTask::MergeFrom(from._internal_sequence());
      break;
    }
    case kManual: {
      _internal_mutable_manual()->::carbon::rtc::ManualTask::MergeFrom(from._internal_manual());
      break;
    }
    case kGoToAndFace: {
      _internal_mutable_go_to_and_face()->::carbon::rtc::GoToAndFaceTask::MergeFrom(from._internal_go_to_and_face());
      break;
    }
    case kFollowPath: {
      _internal_mutable_follow_path()->::carbon::rtc::FollowPathTask::MergeFrom(from._internal_follow_path());
      break;
    }
    case kTractorState: {
      _internal_mutable_tractor_state()->::carbon::rtc::SetTractorStateTask::MergeFrom(from._internal_tractor_state());
      break;
    }
    case kLaserWeed: {
      _internal_mutable_laser_weed()->::carbon::rtc::LaserWeedTask::MergeFrom(from._internal_laser_weed());
      break;
    }
    case kStopAutonomy: {
      _internal_mutable_stop_autonomy()->::carbon::rtc::StopAutonomyTask::MergeFrom(from._internal_stop_autonomy());
      break;
    }
    case kGoToReversiblePath: {
      _internal_mutable_go_to_reversible_path()->::carbon::rtc::GoToReversiblePathTask::MergeFrom(from._internal_go_to_reversible_path());
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Task::CopyFrom(const Task& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.Task)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Task::IsInitialized() const {
  return true;
}

void Task::InternalSwap(Task* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  expected_tractor_state_.InternalSwap(&other->expected_tractor_state_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_info_, lhs_arena,
      &other->status_info_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Task, end_heading_)
      + sizeof(Task::end_heading_)
      - PROTOBUF_FIELD_OFFSET(Task, started_at_)>(
          reinterpret_cast<char*>(&started_at_),
          reinterpret_cast<char*>(&other->started_at_));
  swap(task_details_, other->task_details_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Task::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[5]);
}

// ===================================================================

class StopAutonomyTask::_Internal {
 public:
};

StopAutonomyTask::StopAutonomyTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.StopAutonomyTask)
}
StopAutonomyTask::StopAutonomyTask(const StopAutonomyTask& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.StopAutonomyTask)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StopAutonomyTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StopAutonomyTask::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata StopAutonomyTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[6]);
}

// ===================================================================

class LaserWeedTask::_Internal {
 public:
  static const ::carbon::geo::LineString& path(const LaserWeedTask* msg);
  static const ::carbon::rtc::SpatialPathTolerance& tolerances(const LaserWeedTask* msg);
};

const ::carbon::geo::LineString&
LaserWeedTask::_Internal::path(const LaserWeedTask* msg) {
  return *msg->path_;
}
const ::carbon::rtc::SpatialPathTolerance&
LaserWeedTask::_Internal::tolerances(const LaserWeedTask* msg) {
  return *msg->tolerances_;
}
void LaserWeedTask::clear_path() {
  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
}
LaserWeedTask::LaserWeedTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.LaserWeedTask)
}
LaserWeedTask::LaserWeedTask(const LaserWeedTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_path()) {
    path_ = new ::carbon::geo::LineString(*from.path_);
  } else {
    path_ = nullptr;
  }
  if (from._internal_has_tolerances()) {
    tolerances_ = new ::carbon::rtc::SpatialPathTolerance(*from.tolerances_);
  } else {
    tolerances_ = nullptr;
  }
  ::memcpy(&path_is_reversible_, &from.path_is_reversible_,
    static_cast<size_t>(reinterpret_cast<char*>(&manual_) -
    reinterpret_cast<char*>(&path_is_reversible_)) + sizeof(manual_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.LaserWeedTask)
}

inline void LaserWeedTask::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&path_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&manual_) -
    reinterpret_cast<char*>(&path_)) + sizeof(manual_));
}

LaserWeedTask::~LaserWeedTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.LaserWeedTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserWeedTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete path_;
  if (this != internal_default_instance()) delete tolerances_;
}

void LaserWeedTask::ArenaDtor(void* object) {
  LaserWeedTask* _this = reinterpret_cast< LaserWeedTask* >(object);
  (void)_this;
}
void LaserWeedTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserWeedTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserWeedTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.LaserWeedTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
  if (GetArenaForAllocation() == nullptr && tolerances_ != nullptr) {
    delete tolerances_;
  }
  tolerances_ = nullptr;
  ::memset(&path_is_reversible_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&manual_) -
      reinterpret_cast<char*>(&path_is_reversible_)) + sizeof(manual_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserWeedTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.LineString path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_path(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool path_is_reversible = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          path_is_reversible_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool weeding_enabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          weeding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool thinning_enabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          thinning_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool manual = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          manual_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.SpatialPathTolerance tolerances = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_tolerances(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserWeedTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.LaserWeedTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::path(this), target, stream);
  }

  // bool path_is_reversible = 2;
  if (this->_internal_path_is_reversible() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_path_is_reversible(), target);
  }

  // bool weeding_enabled = 3;
  if (this->_internal_weeding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_weeding_enabled(), target);
  }

  // bool thinning_enabled = 4;
  if (this->_internal_thinning_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_thinning_enabled(), target);
  }

  // bool manual = 5;
  if (this->_internal_manual() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_manual(), target);
  }

  // .carbon.rtc.SpatialPathTolerance tolerances = 6;
  if (this->_internal_has_tolerances()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::tolerances(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.LaserWeedTask)
  return target;
}

size_t LaserWeedTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.LaserWeedTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *path_);
  }

  // .carbon.rtc.SpatialPathTolerance tolerances = 6;
  if (this->_internal_has_tolerances()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *tolerances_);
  }

  // bool path_is_reversible = 2;
  if (this->_internal_path_is_reversible() != 0) {
    total_size += 1 + 1;
  }

  // bool weeding_enabled = 3;
  if (this->_internal_weeding_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool thinning_enabled = 4;
  if (this->_internal_thinning_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool manual = 5;
  if (this->_internal_manual() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserWeedTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserWeedTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserWeedTask::GetClassData() const { return &_class_data_; }

void LaserWeedTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserWeedTask *>(to)->MergeFrom(
      static_cast<const LaserWeedTask &>(from));
}


void LaserWeedTask::MergeFrom(const LaserWeedTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.LaserWeedTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_path()) {
    _internal_mutable_path()->::carbon::geo::LineString::MergeFrom(from._internal_path());
  }
  if (from._internal_has_tolerances()) {
    _internal_mutable_tolerances()->::carbon::rtc::SpatialPathTolerance::MergeFrom(from._internal_tolerances());
  }
  if (from._internal_path_is_reversible() != 0) {
    _internal_set_path_is_reversible(from._internal_path_is_reversible());
  }
  if (from._internal_weeding_enabled() != 0) {
    _internal_set_weeding_enabled(from._internal_weeding_enabled());
  }
  if (from._internal_thinning_enabled() != 0) {
    _internal_set_thinning_enabled(from._internal_thinning_enabled());
  }
  if (from._internal_manual() != 0) {
    _internal_set_manual(from._internal_manual());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserWeedTask::CopyFrom(const LaserWeedTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.LaserWeedTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserWeedTask::IsInitialized() const {
  return true;
}

void LaserWeedTask::InternalSwap(LaserWeedTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserWeedTask, manual_)
      + sizeof(LaserWeedTask::manual_)
      - PROTOBUF_FIELD_OFFSET(LaserWeedTask, path_)>(
          reinterpret_cast<char*>(&path_),
          reinterpret_cast<char*>(&other->path_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserWeedTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[7]);
}

// ===================================================================

class SequenceTask::_Internal {
 public:
};

SequenceTask::SequenceTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  items_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SequenceTask)
}
SequenceTask::SequenceTask(const SequenceTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      items_(from.items_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  atomic_ = from.atomic_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SequenceTask)
}

inline void SequenceTask::SharedCtor() {
atomic_ = false;
}

SequenceTask::~SequenceTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SequenceTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SequenceTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SequenceTask::ArenaDtor(void* object) {
  SequenceTask* _this = reinterpret_cast< SequenceTask* >(object);
  (void)_this;
}
void SequenceTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SequenceTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SequenceTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SequenceTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  items_.Clear();
  atomic_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SequenceTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.Task items = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_items(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool atomic = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          atomic_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SequenceTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SequenceTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.Task items = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_items_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_items(i), target, stream);
  }

  // bool atomic = 2;
  if (this->_internal_atomic() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_atomic(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SequenceTask)
  return target;
}

size_t SequenceTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SequenceTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Task items = 1;
  total_size += 1UL * this->_internal_items_size();
  for (const auto& msg : this->items_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bool atomic = 2;
  if (this->_internal_atomic() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SequenceTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SequenceTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SequenceTask::GetClassData() const { return &_class_data_; }

void SequenceTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SequenceTask *>(to)->MergeFrom(
      static_cast<const SequenceTask &>(from));
}


void SequenceTask::MergeFrom(const SequenceTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SequenceTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  items_.MergeFrom(from.items_);
  if (from._internal_atomic() != 0) {
    _internal_set_atomic(from._internal_atomic());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SequenceTask::CopyFrom(const SequenceTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SequenceTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SequenceTask::IsInitialized() const {
  return true;
}

void SequenceTask::InternalSwap(SequenceTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  items_.InternalSwap(&other->items_);
  swap(atomic_, other->atomic_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SequenceTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[8]);
}

// ===================================================================

class ManualTask::_Internal {
 public:
};

ManualTask::ManualTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ManualTask)
}
ManualTask::ManualTask(const ManualTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  instructions_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    instructions_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_instructions().empty()) {
    instructions_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_instructions(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ManualTask)
}

inline void ManualTask::SharedCtor() {
instructions_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  instructions_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ManualTask::~ManualTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ManualTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ManualTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  instructions_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ManualTask::ArenaDtor(void* object) {
  ManualTask* _this = reinterpret_cast< ManualTask* >(object);
  (void)_this;
}
void ManualTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ManualTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ManualTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ManualTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  instructions_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ManualTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string instructions = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_instructions();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ManualTask.instructions"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ManualTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ManualTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string instructions = 1;
  if (!this->_internal_instructions().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_instructions().data(), static_cast<int>(this->_internal_instructions().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ManualTask.instructions");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_instructions(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ManualTask)
  return target;
}

size_t ManualTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ManualTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string instructions = 1;
  if (!this->_internal_instructions().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_instructions());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ManualTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ManualTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ManualTask::GetClassData() const { return &_class_data_; }

void ManualTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ManualTask *>(to)->MergeFrom(
      static_cast<const ManualTask &>(from));
}


void ManualTask::MergeFrom(const ManualTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ManualTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_instructions().empty()) {
    _internal_set_instructions(from._internal_instructions());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ManualTask::CopyFrom(const ManualTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ManualTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ManualTask::IsInitialized() const {
  return true;
}

void ManualTask::InternalSwap(ManualTask* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &instructions_, lhs_arena,
      &other->instructions_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ManualTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[9]);
}

// ===================================================================

class GoToAndFaceTask::_Internal {
 public:
  static const ::carbon::geo::Point& point(const GoToAndFaceTask* msg);
};

const ::carbon::geo::Point&
GoToAndFaceTask::_Internal::point(const GoToAndFaceTask* msg) {
  return *msg->point_;
}
void GoToAndFaceTask::clear_point() {
  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
}
GoToAndFaceTask::GoToAndFaceTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GoToAndFaceTask)
}
GoToAndFaceTask::GoToAndFaceTask(const GoToAndFaceTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_point()) {
    point_ = new ::carbon::geo::Point(*from.point_);
  } else {
    point_ = nullptr;
  }
  heading_ = from.heading_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GoToAndFaceTask)
}

inline void GoToAndFaceTask::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&point_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&heading_) -
    reinterpret_cast<char*>(&point_)) + sizeof(heading_));
}

GoToAndFaceTask::~GoToAndFaceTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GoToAndFaceTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GoToAndFaceTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete point_;
}

void GoToAndFaceTask::ArenaDtor(void* object) {
  GoToAndFaceTask* _this = reinterpret_cast< GoToAndFaceTask* >(object);
  (void)_this;
}
void GoToAndFaceTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GoToAndFaceTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GoToAndFaceTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GoToAndFaceTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
  heading_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GoToAndFaceTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_point(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double heading = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GoToAndFaceTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GoToAndFaceTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::point(this), target, stream);
  }

  // double heading = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_heading = this->_internal_heading();
  uint64_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_heading(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GoToAndFaceTask)
  return target;
}

size_t GoToAndFaceTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GoToAndFaceTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *point_);
  }

  // double heading = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_heading = this->_internal_heading();
  uint64_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GoToAndFaceTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GoToAndFaceTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GoToAndFaceTask::GetClassData() const { return &_class_data_; }

void GoToAndFaceTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GoToAndFaceTask *>(to)->MergeFrom(
      static_cast<const GoToAndFaceTask &>(from));
}


void GoToAndFaceTask::MergeFrom(const GoToAndFaceTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GoToAndFaceTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_point()) {
    _internal_mutable_point()->::carbon::geo::Point::MergeFrom(from._internal_point());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_heading = from._internal_heading();
  uint64_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _internal_set_heading(from._internal_heading());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GoToAndFaceTask::CopyFrom(const GoToAndFaceTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GoToAndFaceTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GoToAndFaceTask::IsInitialized() const {
  return true;
}

void GoToAndFaceTask::InternalSwap(GoToAndFaceTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GoToAndFaceTask, heading_)
      + sizeof(GoToAndFaceTask::heading_)
      - PROTOBUF_FIELD_OFFSET(GoToAndFaceTask, point_)>(
          reinterpret_cast<char*>(&point_),
          reinterpret_cast<char*>(&other->point_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GoToAndFaceTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[10]);
}

// ===================================================================

class GoToReversiblePathTask::_Internal {
 public:
  static const ::carbon::geo::LineString& path(const GoToReversiblePathTask* msg);
  static const ::carbon::rtc::SpatialPathTolerance& tolerances(const GoToReversiblePathTask* msg);
};

const ::carbon::geo::LineString&
GoToReversiblePathTask::_Internal::path(const GoToReversiblePathTask* msg) {
  return *msg->path_;
}
const ::carbon::rtc::SpatialPathTolerance&
GoToReversiblePathTask::_Internal::tolerances(const GoToReversiblePathTask* msg) {
  return *msg->tolerances_;
}
void GoToReversiblePathTask::clear_path() {
  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
}
GoToReversiblePathTask::GoToReversiblePathTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GoToReversiblePathTask)
}
GoToReversiblePathTask::GoToReversiblePathTask(const GoToReversiblePathTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_path()) {
    path_ = new ::carbon::geo::LineString(*from.path_);
  } else {
    path_ = nullptr;
  }
  if (from._internal_has_tolerances()) {
    tolerances_ = new ::carbon::rtc::SpatialPathTolerance(*from.tolerances_);
  } else {
    tolerances_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GoToReversiblePathTask)
}

inline void GoToReversiblePathTask::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&path_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&tolerances_) -
    reinterpret_cast<char*>(&path_)) + sizeof(tolerances_));
}

GoToReversiblePathTask::~GoToReversiblePathTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GoToReversiblePathTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GoToReversiblePathTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete path_;
  if (this != internal_default_instance()) delete tolerances_;
}

void GoToReversiblePathTask::ArenaDtor(void* object) {
  GoToReversiblePathTask* _this = reinterpret_cast< GoToReversiblePathTask* >(object);
  (void)_this;
}
void GoToReversiblePathTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GoToReversiblePathTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GoToReversiblePathTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GoToReversiblePathTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
  if (GetArenaForAllocation() == nullptr && tolerances_ != nullptr) {
    delete tolerances_;
  }
  tolerances_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GoToReversiblePathTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.LineString path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_path(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.SpatialPathTolerance tolerances = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_tolerances(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GoToReversiblePathTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GoToReversiblePathTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::path(this), target, stream);
  }

  // .carbon.rtc.SpatialPathTolerance tolerances = 2;
  if (this->_internal_has_tolerances()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::tolerances(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GoToReversiblePathTask)
  return target;
}

size_t GoToReversiblePathTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GoToReversiblePathTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *path_);
  }

  // .carbon.rtc.SpatialPathTolerance tolerances = 2;
  if (this->_internal_has_tolerances()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *tolerances_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GoToReversiblePathTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GoToReversiblePathTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GoToReversiblePathTask::GetClassData() const { return &_class_data_; }

void GoToReversiblePathTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GoToReversiblePathTask *>(to)->MergeFrom(
      static_cast<const GoToReversiblePathTask &>(from));
}


void GoToReversiblePathTask::MergeFrom(const GoToReversiblePathTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GoToReversiblePathTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_path()) {
    _internal_mutable_path()->::carbon::geo::LineString::MergeFrom(from._internal_path());
  }
  if (from._internal_has_tolerances()) {
    _internal_mutable_tolerances()->::carbon::rtc::SpatialPathTolerance::MergeFrom(from._internal_tolerances());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GoToReversiblePathTask::CopyFrom(const GoToReversiblePathTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GoToReversiblePathTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GoToReversiblePathTask::IsInitialized() const {
  return true;
}

void GoToReversiblePathTask::InternalSwap(GoToReversiblePathTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GoToReversiblePathTask, tolerances_)
      + sizeof(GoToReversiblePathTask::tolerances_)
      - PROTOBUF_FIELD_OFFSET(GoToReversiblePathTask, path_)>(
          reinterpret_cast<char*>(&path_),
          reinterpret_cast<char*>(&other->path_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GoToReversiblePathTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[11]);
}

// ===================================================================

class FollowPathTask::_Internal {
 public:
  static const ::carbon::geo::LineString& path(const FollowPathTask* msg);
  static const ::carbon::rtc::SpeedSetting& speed(const FollowPathTask* msg);
};

const ::carbon::geo::LineString&
FollowPathTask::_Internal::path(const FollowPathTask* msg) {
  return *msg->path_;
}
const ::carbon::rtc::SpeedSetting&
FollowPathTask::_Internal::speed(const FollowPathTask* msg) {
  return *msg->speed_;
}
void FollowPathTask::clear_path() {
  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
}
FollowPathTask::FollowPathTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.FollowPathTask)
}
FollowPathTask::FollowPathTask(const FollowPathTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_path()) {
    path_ = new ::carbon::geo::LineString(*from.path_);
  } else {
    path_ = nullptr;
  }
  if (from._internal_has_speed()) {
    speed_ = new ::carbon::rtc::SpeedSetting(*from.speed_);
  } else {
    speed_ = nullptr;
  }
  stop_on_completion_ = from.stop_on_completion_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.FollowPathTask)
}

inline void FollowPathTask::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&path_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&stop_on_completion_) -
    reinterpret_cast<char*>(&path_)) + sizeof(stop_on_completion_));
}

FollowPathTask::~FollowPathTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.FollowPathTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FollowPathTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete path_;
  if (this != internal_default_instance()) delete speed_;
}

void FollowPathTask::ArenaDtor(void* object) {
  FollowPathTask* _this = reinterpret_cast< FollowPathTask* >(object);
  (void)_this;
}
void FollowPathTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FollowPathTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FollowPathTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.FollowPathTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && path_ != nullptr) {
    delete path_;
  }
  path_ = nullptr;
  if (GetArenaForAllocation() == nullptr && speed_ != nullptr) {
    delete speed_;
  }
  speed_ = nullptr;
  stop_on_completion_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FollowPathTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.LineString path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_path(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.SpeedSetting speed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_speed(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool stop_on_completion = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          stop_on_completion_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FollowPathTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.FollowPathTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::path(this), target, stream);
  }

  // .carbon.rtc.SpeedSetting speed = 2;
  if (this->_internal_has_speed()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::speed(this), target, stream);
  }

  // bool stop_on_completion = 3;
  if (this->_internal_stop_on_completion() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_stop_on_completion(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.FollowPathTask)
  return target;
}

size_t FollowPathTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.FollowPathTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.LineString path = 1;
  if (this->_internal_has_path()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *path_);
  }

  // .carbon.rtc.SpeedSetting speed = 2;
  if (this->_internal_has_speed()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *speed_);
  }

  // bool stop_on_completion = 3;
  if (this->_internal_stop_on_completion() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FollowPathTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FollowPathTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FollowPathTask::GetClassData() const { return &_class_data_; }

void FollowPathTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FollowPathTask *>(to)->MergeFrom(
      static_cast<const FollowPathTask &>(from));
}


void FollowPathTask::MergeFrom(const FollowPathTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.FollowPathTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_path()) {
    _internal_mutable_path()->::carbon::geo::LineString::MergeFrom(from._internal_path());
  }
  if (from._internal_has_speed()) {
    _internal_mutable_speed()->::carbon::rtc::SpeedSetting::MergeFrom(from._internal_speed());
  }
  if (from._internal_stop_on_completion() != 0) {
    _internal_set_stop_on_completion(from._internal_stop_on_completion());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FollowPathTask::CopyFrom(const FollowPathTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.FollowPathTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FollowPathTask::IsInitialized() const {
  return true;
}

void FollowPathTask::InternalSwap(FollowPathTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FollowPathTask, stop_on_completion_)
      + sizeof(FollowPathTask::stop_on_completion_)
      - PROTOBUF_FIELD_OFFSET(FollowPathTask, path_)>(
          reinterpret_cast<char*>(&path_),
          reinterpret_cast<char*>(&other->path_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FollowPathTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[12]);
}

// ===================================================================

class SpeedSetting::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Empty& remote_operator_controlled(const SpeedSetting* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Empty& implement_controlled(const SpeedSetting* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Empty&
SpeedSetting::_Internal::remote_operator_controlled(const SpeedSetting* msg) {
  return *msg->speed_.remote_operator_controlled_;
}
const ::PROTOBUF_NAMESPACE_ID::Empty&
SpeedSetting::_Internal::implement_controlled(const SpeedSetting* msg) {
  return *msg->speed_.implement_controlled_;
}
void SpeedSetting::set_allocated_remote_operator_controlled(::PROTOBUF_NAMESPACE_ID::Empty* remote_operator_controlled) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_speed();
  if (remote_operator_controlled) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(remote_operator_controlled));
    if (message_arena != submessage_arena) {
      remote_operator_controlled = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, remote_operator_controlled, submessage_arena);
    }
    set_has_remote_operator_controlled();
    speed_.remote_operator_controlled_ = remote_operator_controlled;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.SpeedSetting.remote_operator_controlled)
}
void SpeedSetting::clear_remote_operator_controlled() {
  if (_internal_has_remote_operator_controlled()) {
    if (GetArenaForAllocation() == nullptr) {
      delete speed_.remote_operator_controlled_;
    }
    clear_has_speed();
  }
}
void SpeedSetting::set_allocated_implement_controlled(::PROTOBUF_NAMESPACE_ID::Empty* implement_controlled) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_speed();
  if (implement_controlled) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(implement_controlled));
    if (message_arena != submessage_arena) {
      implement_controlled = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, implement_controlled, submessage_arena);
    }
    set_has_implement_controlled();
    speed_.implement_controlled_ = implement_controlled;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.SpeedSetting.implement_controlled)
}
void SpeedSetting::clear_implement_controlled() {
  if (_internal_has_implement_controlled()) {
    if (GetArenaForAllocation() == nullptr) {
      delete speed_.implement_controlled_;
    }
    clear_has_speed();
  }
}
SpeedSetting::SpeedSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SpeedSetting)
}
SpeedSetting::SpeedSetting(const SpeedSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_speed();
  switch (from.speed_case()) {
    case kConstantMph: {
      _internal_set_constant_mph(from._internal_constant_mph());
      break;
    }
    case kRemoteOperatorControlled: {
      _internal_mutable_remote_operator_controlled()->::PROTOBUF_NAMESPACE_ID::Empty::MergeFrom(from._internal_remote_operator_controlled());
      break;
    }
    case kImplementControlled: {
      _internal_mutable_implement_controlled()->::PROTOBUF_NAMESPACE_ID::Empty::MergeFrom(from._internal_implement_controlled());
      break;
    }
    case SPEED_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SpeedSetting)
}

inline void SpeedSetting::SharedCtor() {
clear_has_speed();
}

SpeedSetting::~SpeedSetting() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SpeedSetting)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpeedSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_speed()) {
    clear_speed();
  }
}

void SpeedSetting::ArenaDtor(void* object) {
  SpeedSetting* _this = reinterpret_cast< SpeedSetting* >(object);
  (void)_this;
}
void SpeedSetting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpeedSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpeedSetting::clear_speed() {
// @@protoc_insertion_point(one_of_clear_start:carbon.rtc.SpeedSetting)
  switch (speed_case()) {
    case kConstantMph: {
      // No need to clear
      break;
    }
    case kRemoteOperatorControlled: {
      if (GetArenaForAllocation() == nullptr) {
        delete speed_.remote_operator_controlled_;
      }
      break;
    }
    case kImplementControlled: {
      if (GetArenaForAllocation() == nullptr) {
        delete speed_.implement_controlled_;
      }
      break;
    }
    case SPEED_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = SPEED_NOT_SET;
}


void SpeedSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SpeedSetting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_speed();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpeedSetting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double constant_mph = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_constant_mph(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Empty remote_operator_controlled = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_remote_operator_controlled(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Empty implement_controlled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_implement_controlled(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpeedSetting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SpeedSetting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double constant_mph = 1;
  if (_internal_has_constant_mph()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_constant_mph(), target);
  }

  // .google.protobuf.Empty remote_operator_controlled = 2;
  if (_internal_has_remote_operator_controlled()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::remote_operator_controlled(this), target, stream);
  }

  // .google.protobuf.Empty implement_controlled = 3;
  if (_internal_has_implement_controlled()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::implement_controlled(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SpeedSetting)
  return target;
}

size_t SpeedSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SpeedSetting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (speed_case()) {
    // double constant_mph = 1;
    case kConstantMph: {
      total_size += 1 + 8;
      break;
    }
    // .google.protobuf.Empty remote_operator_controlled = 2;
    case kRemoteOperatorControlled: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *speed_.remote_operator_controlled_);
      break;
    }
    // .google.protobuf.Empty implement_controlled = 3;
    case kImplementControlled: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *speed_.implement_controlled_);
      break;
    }
    case SPEED_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpeedSetting::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpeedSetting::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpeedSetting::GetClassData() const { return &_class_data_; }

void SpeedSetting::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpeedSetting *>(to)->MergeFrom(
      static_cast<const SpeedSetting &>(from));
}


void SpeedSetting::MergeFrom(const SpeedSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SpeedSetting)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.speed_case()) {
    case kConstantMph: {
      _internal_set_constant_mph(from._internal_constant_mph());
      break;
    }
    case kRemoteOperatorControlled: {
      _internal_mutable_remote_operator_controlled()->::PROTOBUF_NAMESPACE_ID::Empty::MergeFrom(from._internal_remote_operator_controlled());
      break;
    }
    case kImplementControlled: {
      _internal_mutable_implement_controlled()->::PROTOBUF_NAMESPACE_ID::Empty::MergeFrom(from._internal_implement_controlled());
      break;
    }
    case SPEED_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpeedSetting::CopyFrom(const SpeedSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SpeedSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpeedSetting::IsInitialized() const {
  return true;
}

void SpeedSetting::InternalSwap(SpeedSetting* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(speed_, other->speed_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata SpeedSetting::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[13]);
}

// ===================================================================

class SetTractorStateTask::_Internal {
 public:
};

SetTractorStateTask::SetTractorStateTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  state_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SetTractorStateTask)
}
SetTractorStateTask::SetTractorStateTask(const SetTractorStateTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      state_(from.state_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SetTractorStateTask)
}

inline void SetTractorStateTask::SharedCtor() {
}

SetTractorStateTask::~SetTractorStateTask() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SetTractorStateTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetTractorStateTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetTractorStateTask::ArenaDtor(void* object) {
  SetTractorStateTask* _this = reinterpret_cast< SetTractorStateTask* >(object);
  (void)_this;
}
void SetTractorStateTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetTractorStateTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetTractorStateTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SetTractorStateTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  state_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetTractorStateTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.TractorState state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_state(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetTractorStateTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SetTractorStateTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.TractorState state = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_state_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_state(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SetTractorStateTask)
  return target;
}

size_t SetTractorStateTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SetTractorStateTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.TractorState state = 1;
  total_size += 1UL * this->_internal_state_size();
  for (const auto& msg : this->state_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetTractorStateTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetTractorStateTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetTractorStateTask::GetClassData() const { return &_class_data_; }

void SetTractorStateTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetTractorStateTask *>(to)->MergeFrom(
      static_cast<const SetTractorStateTask &>(from));
}


void SetTractorStateTask::MergeFrom(const SetTractorStateTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SetTractorStateTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  state_.MergeFrom(from.state_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetTractorStateTask::CopyFrom(const SetTractorStateTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SetTractorStateTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetTractorStateTask::IsInitialized() const {
  return true;
}

void SetTractorStateTask::InternalSwap(SetTractorStateTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  state_.InternalSwap(&other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetTractorStateTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[14]);
}

// ===================================================================

class TractorState::_Internal {
 public:
  static const ::carbon::rtc::HitchState& hitch(const TractorState* msg);
};

const ::carbon::rtc::HitchState&
TractorState::_Internal::hitch(const TractorState* msg) {
  return *msg->state_.hitch_;
}
void TractorState::set_allocated_hitch(::carbon::rtc::HitchState* hitch) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_state();
  if (hitch) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::HitchState>::GetOwningArena(hitch);
    if (message_arena != submessage_arena) {
      hitch = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hitch, submessage_arena);
    }
    set_has_hitch();
    state_.hitch_ = hitch;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.TractorState.hitch)
}
TractorState::TractorState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.TractorState)
}
TractorState::TractorState(const TractorState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_state();
  switch (from.state_case()) {
    case kGear: {
      _internal_set_gear(from._internal_gear());
      break;
    }
    case kHitch: {
      _internal_mutable_hitch()->::carbon::rtc::HitchState::MergeFrom(from._internal_hitch());
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.TractorState)
}

inline void TractorState::SharedCtor() {
clear_has_state();
}

TractorState::~TractorState() {
  // @@protoc_insertion_point(destructor:carbon.rtc.TractorState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TractorState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_state()) {
    clear_state();
  }
}

void TractorState::ArenaDtor(void* object) {
  TractorState* _this = reinterpret_cast< TractorState* >(object);
  (void)_this;
}
void TractorState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TractorState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TractorState::clear_state() {
// @@protoc_insertion_point(one_of_clear_start:carbon.rtc.TractorState)
  switch (state_case()) {
    case kGear: {
      // No need to clear
      break;
    }
    case kHitch: {
      if (GetArenaForAllocation() == nullptr) {
        delete state_.hitch_;
      }
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = STATE_NOT_SET;
}


void TractorState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.TractorState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_state();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TractorState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.TractorState.Gear gear = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_gear(static_cast<::carbon::rtc::TractorState_Gear>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.HitchState hitch = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_hitch(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TractorState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.TractorState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.TractorState.Gear gear = 1;
  if (_internal_has_gear()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_gear(), target);
  }

  // .carbon.rtc.HitchState hitch = 2;
  if (_internal_has_hitch()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::hitch(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.TractorState)
  return target;
}

size_t TractorState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.TractorState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (state_case()) {
    // .carbon.rtc.TractorState.Gear gear = 1;
    case kGear: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_gear());
      break;
    }
    // .carbon.rtc.HitchState hitch = 2;
    case kHitch: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *state_.hitch_);
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TractorState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TractorState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TractorState::GetClassData() const { return &_class_data_; }

void TractorState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TractorState *>(to)->MergeFrom(
      static_cast<const TractorState &>(from));
}


void TractorState::MergeFrom(const TractorState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.TractorState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.state_case()) {
    case kGear: {
      _internal_set_gear(from._internal_gear());
      break;
    }
    case kHitch: {
      _internal_mutable_hitch()->::carbon::rtc::HitchState::MergeFrom(from._internal_hitch());
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TractorState::CopyFrom(const TractorState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.TractorState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TractorState::IsInitialized() const {
  return true;
}

void TractorState::InternalSwap(TractorState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(state_, other->state_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TractorState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[15]);
}

// ===================================================================

class HitchState::_Internal {
 public:
};

HitchState::HitchState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.HitchState)
}
HitchState::HitchState(const HitchState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_state();
  switch (from.state_case()) {
    case kCommand: {
      _internal_set_command(from._internal_command());
      break;
    }
    case kPosition: {
      _internal_set_position(from._internal_position());
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.HitchState)
}

inline void HitchState::SharedCtor() {
clear_has_state();
}

HitchState::~HitchState() {
  // @@protoc_insertion_point(destructor:carbon.rtc.HitchState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HitchState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_state()) {
    clear_state();
  }
}

void HitchState::ArenaDtor(void* object) {
  HitchState* _this = reinterpret_cast< HitchState* >(object);
  (void)_this;
}
void HitchState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HitchState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HitchState::clear_state() {
// @@protoc_insertion_point(one_of_clear_start:carbon.rtc.HitchState)
  switch (state_case()) {
    case kCommand: {
      // No need to clear
      break;
    }
    case kPosition: {
      // No need to clear
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = STATE_NOT_SET;
}


void HitchState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.HitchState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_state();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HitchState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.HitchState.HitchCommand command = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_command(static_cast<::carbon::rtc::HitchState_HitchCommand>(val));
        } else
          goto handle_unusual;
        continue;
      // double position = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _internal_set_position(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HitchState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.HitchState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.HitchState.HitchCommand command = 1;
  if (_internal_has_command()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_command(), target);
  }

  // double position = 2;
  if (_internal_has_position()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_position(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.HitchState)
  return target;
}

size_t HitchState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.HitchState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (state_case()) {
    // .carbon.rtc.HitchState.HitchCommand command = 1;
    case kCommand: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_command());
      break;
    }
    // double position = 2;
    case kPosition: {
      total_size += 1 + 8;
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HitchState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HitchState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HitchState::GetClassData() const { return &_class_data_; }

void HitchState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HitchState *>(to)->MergeFrom(
      static_cast<const HitchState &>(from));
}


void HitchState::MergeFrom(const HitchState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.HitchState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.state_case()) {
    case kCommand: {
      _internal_set_command(from._internal_command());
      break;
    }
    case kPosition: {
      _internal_set_position(from._internal_position());
      break;
    }
    case STATE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HitchState::CopyFrom(const HitchState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.HitchState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HitchState::IsInitialized() const {
  return true;
}

void HitchState::InternalSwap(HitchState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(state_, other->state_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata HitchState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[16]);
}

// ===================================================================

class TaskList::_Internal {
 public:
};

TaskList::TaskList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  tasks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.TaskList)
}
TaskList::TaskList(const TaskList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      tasks_(from.tasks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.TaskList)
}

inline void TaskList::SharedCtor() {
}

TaskList::~TaskList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.TaskList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TaskList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TaskList::ArenaDtor(void* object) {
  TaskList* _this = reinterpret_cast< TaskList* >(object);
  (void)_this;
}
void TaskList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TaskList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TaskList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.TaskList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tasks_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TaskList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.Task tasks = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tasks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TaskList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.TaskList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.Task tasks = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tasks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_tasks(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.TaskList)
  return target;
}

size_t TaskList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.TaskList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Task tasks = 1;
  total_size += 1UL * this->_internal_tasks_size();
  for (const auto& msg : this->tasks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TaskList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TaskList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TaskList::GetClassData() const { return &_class_data_; }

void TaskList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TaskList *>(to)->MergeFrom(
      static_cast<const TaskList &>(from));
}


void TaskList::MergeFrom(const TaskList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.TaskList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  tasks_.MergeFrom(from.tasks_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TaskList::CopyFrom(const TaskList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.TaskList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TaskList::IsInitialized() const {
  return true;
}

void TaskList::InternalSwap(TaskList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  tasks_.InternalSwap(&other->tasks_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TaskList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[17]);
}

// ===================================================================

class ListTasksResponse::_Internal {
 public:
};

ListTasksResponse::ListTasksResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  tasks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListTasksResponse)
}
ListTasksResponse::ListTasksResponse(const ListTasksResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      tasks_(from.tasks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListTasksResponse)
}

inline void ListTasksResponse::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListTasksResponse::~ListTasksResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListTasksResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListTasksResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListTasksResponse::ArenaDtor(void* object) {
  ListTasksResponse* _this = reinterpret_cast< ListTasksResponse* >(object);
  (void)_this;
}
void ListTasksResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListTasksResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListTasksResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListTasksResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tasks_.Clear();
  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListTasksResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListTasksResponse.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Task tasks = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tasks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListTasksResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListTasksResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListTasksResponse.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  // repeated .carbon.rtc.Task tasks = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tasks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_tasks(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListTasksResponse)
  return target;
}

size_t ListTasksResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListTasksResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Task tasks = 2;
  total_size += 1UL * this->_internal_tasks_size();
  for (const auto& msg : this->tasks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListTasksResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListTasksResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListTasksResponse::GetClassData() const { return &_class_data_; }

void ListTasksResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListTasksResponse *>(to)->MergeFrom(
      static_cast<const ListTasksResponse &>(from));
}


void ListTasksResponse::MergeFrom(const ListTasksResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListTasksResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  tasks_.MergeFrom(from.tasks_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListTasksResponse::CopyFrom(const ListTasksResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListTasksResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListTasksResponse::IsInitialized() const {
  return true;
}

void ListTasksResponse::InternalSwap(ListTasksResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  tasks_.InternalSwap(&other->tasks_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListTasksResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[18]);
}

// ===================================================================

class SpatialPathTolerance::_Internal {
 public:
};

SpatialPathTolerance::SpatialPathTolerance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SpatialPathTolerance)
}
SpatialPathTolerance::SpatialPathTolerance(const SpatialPathTolerance& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&heading_, &from.heading_,
    static_cast<size_t>(reinterpret_cast<char*>(&continuous_crosstrack_) -
    reinterpret_cast<char*>(&heading_)) + sizeof(continuous_crosstrack_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SpatialPathTolerance)
}

inline void SpatialPathTolerance::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&heading_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&continuous_crosstrack_) -
    reinterpret_cast<char*>(&heading_)) + sizeof(continuous_crosstrack_));
}

SpatialPathTolerance::~SpatialPathTolerance() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SpatialPathTolerance)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialPathTolerance::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialPathTolerance::ArenaDtor(void* object) {
  SpatialPathTolerance* _this = reinterpret_cast< SpatialPathTolerance* >(object);
  (void)_this;
}
void SpatialPathTolerance::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialPathTolerance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialPathTolerance::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SpatialPathTolerance)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&heading_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&continuous_crosstrack_) -
      reinterpret_cast<char*>(&heading_)) + sizeof(continuous_crosstrack_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialPathTolerance::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float heading = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float crosstrack = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          crosstrack_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float distance = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float continuous_crosstrack = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          continuous_crosstrack_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialPathTolerance::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SpatialPathTolerance)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_heading(), target);
  }

  // float crosstrack = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crosstrack = this->_internal_crosstrack();
  uint32_t raw_crosstrack;
  memcpy(&raw_crosstrack, &tmp_crosstrack, sizeof(tmp_crosstrack));
  if (raw_crosstrack != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_crosstrack(), target);
  }

  // float distance = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_distance(), target);
  }

  // float continuous_crosstrack = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_continuous_crosstrack = this->_internal_continuous_crosstrack();
  uint32_t raw_continuous_crosstrack;
  memcpy(&raw_continuous_crosstrack, &tmp_continuous_crosstrack, sizeof(tmp_continuous_crosstrack));
  if (raw_continuous_crosstrack != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_continuous_crosstrack(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SpatialPathTolerance)
  return target;
}

size_t SpatialPathTolerance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SpatialPathTolerance)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 4;
  }

  // float crosstrack = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crosstrack = this->_internal_crosstrack();
  uint32_t raw_crosstrack;
  memcpy(&raw_crosstrack, &tmp_crosstrack, sizeof(tmp_crosstrack));
  if (raw_crosstrack != 0) {
    total_size += 1 + 4;
  }

  // float distance = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = this->_internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    total_size += 1 + 4;
  }

  // float continuous_crosstrack = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_continuous_crosstrack = this->_internal_continuous_crosstrack();
  uint32_t raw_continuous_crosstrack;
  memcpy(&raw_continuous_crosstrack, &tmp_continuous_crosstrack, sizeof(tmp_continuous_crosstrack));
  if (raw_continuous_crosstrack != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialPathTolerance::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialPathTolerance::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialPathTolerance::GetClassData() const { return &_class_data_; }

void SpatialPathTolerance::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialPathTolerance *>(to)->MergeFrom(
      static_cast<const SpatialPathTolerance &>(from));
}


void SpatialPathTolerance::MergeFrom(const SpatialPathTolerance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SpatialPathTolerance)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = from._internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _internal_set_heading(from._internal_heading());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crosstrack = from._internal_crosstrack();
  uint32_t raw_crosstrack;
  memcpy(&raw_crosstrack, &tmp_crosstrack, sizeof(tmp_crosstrack));
  if (raw_crosstrack != 0) {
    _internal_set_crosstrack(from._internal_crosstrack());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_distance = from._internal_distance();
  uint32_t raw_distance;
  memcpy(&raw_distance, &tmp_distance, sizeof(tmp_distance));
  if (raw_distance != 0) {
    _internal_set_distance(from._internal_distance());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_continuous_crosstrack = from._internal_continuous_crosstrack();
  uint32_t raw_continuous_crosstrack;
  memcpy(&raw_continuous_crosstrack, &tmp_continuous_crosstrack, sizeof(tmp_continuous_crosstrack));
  if (raw_continuous_crosstrack != 0) {
    _internal_set_continuous_crosstrack(from._internal_continuous_crosstrack());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialPathTolerance::CopyFrom(const SpatialPathTolerance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SpatialPathTolerance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialPathTolerance::IsInitialized() const {
  return true;
}

void SpatialPathTolerance::InternalSwap(SpatialPathTolerance* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SpatialPathTolerance, continuous_crosstrack_)
      + sizeof(SpatialPathTolerance::continuous_crosstrack_)
      - PROTOBUF_FIELD_OFFSET(SpatialPathTolerance, heading_)>(
          reinterpret_cast<char*>(&heading_),
          reinterpret_cast<char*>(&other->heading_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialPathTolerance::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[19]);
}

// ===================================================================

class Job::_Internal {
 public:
  using HasBits = decltype(std::declval<Job>()._has_bits_);
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& started_at(const Job* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& ended_at(const Job* msg);
  static void set_has_work_order_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
Job::_Internal::started_at(const Job* msg) {
  return *msg->started_at_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
Job::_Internal::ended_at(const Job* msg) {
  return *msg->ended_at_;
}
void Job::clear_started_at() {
  if (GetArenaForAllocation() == nullptr && started_at_ != nullptr) {
    delete started_at_;
  }
  started_at_ = nullptr;
}
void Job::clear_ended_at() {
  if (GetArenaForAllocation() == nullptr && ended_at_ != nullptr) {
    delete ended_at_;
  }
  ended_at_ = nullptr;
}
Job::Job(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  objectives_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.Job)
}
Job::Job(const Job& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      objectives_(from.objectives_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  farm_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    farm_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_farm_id().empty()) {
    farm_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_farm_id(), 
      GetArenaForAllocation());
  }
  field_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    field_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_field_id().empty()) {
    field_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_field_id(), 
      GetArenaForAllocation());
  }
  customer_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    customer_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_customer_id().empty()) {
    customer_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_customer_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_started_at()) {
    started_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.started_at_);
  } else {
    started_at_ = nullptr;
  }
  if (from._internal_has_ended_at()) {
    ended_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.ended_at_);
  } else {
    ended_at_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&id_)) + sizeof(priority_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.Job)
}

inline void Job::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
farm_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  farm_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
field_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  field_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
customer_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  customer_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&started_at_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&started_at_)) + sizeof(priority_));
}

Job::~Job() {
  // @@protoc_insertion_point(destructor:carbon.rtc.Job)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Job::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  farm_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  field_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  customer_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete started_at_;
  if (this != internal_default_instance()) delete ended_at_;
}

void Job::ArenaDtor(void* object) {
  Job* _this = reinterpret_cast< Job* >(object);
  (void)_this;
}
void Job::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Job::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Job::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.Job)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  objectives_.Clear();
  name_.ClearToEmpty();
  farm_id_.ClearToEmpty();
  field_id_.ClearToEmpty();
  customer_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && started_at_ != nullptr) {
    delete started_at_;
  }
  started_at_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ended_at_ != nullptr) {
    delete ended_at_;
  }
  ended_at_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&id_)) + sizeof(type_));
  work_order_id_ = uint64_t{0u};
  priority_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Job::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Job.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp started_at = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_started_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp ended_at = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_ended_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Objective objectives = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objectives(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.State state = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::State>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.Job.JobType type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::rtc::Job_JobType>(val));
        } else
          goto handle_unusual;
        continue;
      // optional uint64 work_order_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_work_order_id(&has_bits);
          work_order_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string farm_id = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_farm_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Job.farm_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string field_id = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_field_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Job.field_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string customer_id = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_customer_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Job.customer_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Job::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.Job)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Job.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // .google.protobuf.Timestamp started_at = 3;
  if (this->_internal_has_started_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::started_at(this), target, stream);
  }

  // .google.protobuf.Timestamp ended_at = 4;
  if (this->_internal_has_ended_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::ended_at(this), target, stream);
  }

  // repeated .carbon.rtc.Objective objectives = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_objectives_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_objectives(i), target, stream);
  }

  // .carbon.rtc.State state = 6;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_state(), target);
  }

  // .carbon.rtc.Job.JobType type = 7;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_type(), target);
  }

  // optional uint64 work_order_id = 8;
  if (_internal_has_work_order_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(8, this->_internal_work_order_id(), target);
  }

  // string farm_id = 9;
  if (!this->_internal_farm_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_farm_id().data(), static_cast<int>(this->_internal_farm_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Job.farm_id");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_farm_id(), target);
  }

  // string field_id = 10;
  if (!this->_internal_field_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_field_id().data(), static_cast<int>(this->_internal_field_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Job.field_id");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_field_id(), target);
  }

  // string customer_id = 11;
  if (!this->_internal_customer_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_customer_id().data(), static_cast<int>(this->_internal_customer_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Job.customer_id");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_customer_id(), target);
  }

  // int32 priority = 12;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(12, this->_internal_priority(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.Job)
  return target;
}

size_t Job::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.Job)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Objective objectives = 5;
  total_size += 1UL * this->_internal_objectives_size();
  for (const auto& msg : this->objectives_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string farm_id = 9;
  if (!this->_internal_farm_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_farm_id());
  }

  // string field_id = 10;
  if (!this->_internal_field_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_field_id());
  }

  // string customer_id = 11;
  if (!this->_internal_customer_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_customer_id());
  }

  // .google.protobuf.Timestamp started_at = 3;
  if (this->_internal_has_started_at()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *started_at_);
  }

  // .google.protobuf.Timestamp ended_at = 4;
  if (this->_internal_has_ended_at()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ended_at_);
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  // .carbon.rtc.State state = 6;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  // .carbon.rtc.Job.JobType type = 7;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // optional uint64 work_order_id = 8;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_work_order_id());
  }

  // int32 priority = 12;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Job::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Job::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Job::GetClassData() const { return &_class_data_; }

void Job::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Job *>(to)->MergeFrom(
      static_cast<const Job &>(from));
}


void Job::MergeFrom(const Job& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.Job)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  objectives_.MergeFrom(from.objectives_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_farm_id().empty()) {
    _internal_set_farm_id(from._internal_farm_id());
  }
  if (!from._internal_field_id().empty()) {
    _internal_set_field_id(from._internal_field_id());
  }
  if (!from._internal_customer_id().empty()) {
    _internal_set_customer_id(from._internal_customer_id());
  }
  if (from._internal_has_started_at()) {
    _internal_mutable_started_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_started_at());
  }
  if (from._internal_has_ended_at()) {
    _internal_mutable_ended_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_ended_at());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_has_work_order_id()) {
    _internal_set_work_order_id(from._internal_work_order_id());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Job::CopyFrom(const Job& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.Job)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Job::IsInitialized() const {
  return true;
}

void Job::InternalSwap(Job* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  objectives_.InternalSwap(&other->objectives_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &farm_id_, lhs_arena,
      &other->farm_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &field_id_, lhs_arena,
      &other->field_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &customer_id_, lhs_arena,
      &other->customer_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Job, priority_)
      + sizeof(Job::priority_)
      - PROTOBUF_FIELD_OFFSET(Job, started_at_)>(
          reinterpret_cast<char*>(&started_at_),
          reinterpret_cast<char*>(&other->started_at_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Job::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[20]);
}

// ===================================================================

class JobList::_Internal {
 public:
};

JobList::JobList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  jobs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.JobList)
}
JobList::JobList(const JobList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      jobs_(from.jobs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.JobList)
}

inline void JobList::SharedCtor() {
}

JobList::~JobList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.JobList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void JobList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void JobList::ArenaDtor(void* object) {
  JobList* _this = reinterpret_cast< JobList* >(object);
  (void)_this;
}
void JobList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void JobList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.JobList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobs_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.Job jobs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_jobs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JobList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.JobList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.Job jobs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_jobs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_jobs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.JobList)
  return target;
}

size_t JobList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.JobList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Job jobs = 1;
  total_size += 1UL * this->_internal_jobs_size();
  for (const auto& msg : this->jobs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JobList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    JobList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JobList::GetClassData() const { return &_class_data_; }

void JobList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<JobList *>(to)->MergeFrom(
      static_cast<const JobList &>(from));
}


void JobList::MergeFrom(const JobList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.JobList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  jobs_.MergeFrom(from.jobs_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JobList::CopyFrom(const JobList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.JobList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobList::IsInitialized() const {
  return true;
}

void JobList::InternalSwap(JobList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  jobs_.InternalSwap(&other->jobs_);
}

::PROTOBUF_NAMESPACE_ID::Metadata JobList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[21]);
}

// ===================================================================

class ListJobsResponse::_Internal {
 public:
};

ListJobsResponse::ListJobsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  jobs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListJobsResponse)
}
ListJobsResponse::ListJobsResponse(const ListJobsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      jobs_(from.jobs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListJobsResponse)
}

inline void ListJobsResponse::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListJobsResponse::~ListJobsResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListJobsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListJobsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListJobsResponse::ArenaDtor(void* object) {
  ListJobsResponse* _this = reinterpret_cast< ListJobsResponse* >(object);
  (void)_this;
}
void ListJobsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListJobsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListJobsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListJobsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobs_.Clear();
  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListJobsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListJobsResponse.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Job jobs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_jobs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListJobsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListJobsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListJobsResponse.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  // repeated .carbon.rtc.Job jobs = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_jobs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_jobs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListJobsResponse)
  return target;
}

size_t ListJobsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListJobsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Job jobs = 2;
  total_size += 1UL * this->_internal_jobs_size();
  for (const auto& msg : this->jobs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListJobsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListJobsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListJobsResponse::GetClassData() const { return &_class_data_; }

void ListJobsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListJobsResponse *>(to)->MergeFrom(
      static_cast<const ListJobsResponse &>(from));
}


void ListJobsResponse::MergeFrom(const ListJobsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListJobsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  jobs_.MergeFrom(from.jobs_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListJobsResponse::CopyFrom(const ListJobsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListJobsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListJobsResponse::IsInitialized() const {
  return true;
}

void ListJobsResponse::InternalSwap(ListJobsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  jobs_.InternalSwap(&other->jobs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListJobsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[22]);
}

// ===================================================================

class WorkOrder::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& scheduled_at(const WorkOrder* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
WorkOrder::_Internal::scheduled_at(const WorkOrder* msg) {
  return *msg->scheduled_at_;
}
void WorkOrder::clear_scheduled_at() {
  if (GetArenaForAllocation() == nullptr && scheduled_at_ != nullptr) {
    delete scheduled_at_;
  }
  scheduled_at_ = nullptr;
}
WorkOrder::WorkOrder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  jobs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.WorkOrder)
}
WorkOrder::WorkOrder(const WorkOrder& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      jobs_(from.jobs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_scheduled_at()) {
    scheduled_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.scheduled_at_);
  } else {
    scheduled_at_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&id_)) + sizeof(state_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.WorkOrder)
}

inline void WorkOrder::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&scheduled_at_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&scheduled_at_)) + sizeof(state_));
}

WorkOrder::~WorkOrder() {
  // @@protoc_insertion_point(destructor:carbon.rtc.WorkOrder)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WorkOrder::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete scheduled_at_;
}

void WorkOrder::ArenaDtor(void* object) {
  WorkOrder* _this = reinterpret_cast< WorkOrder* >(object);
  (void)_this;
}
void WorkOrder::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WorkOrder::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WorkOrder::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.WorkOrder)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobs_.Clear();
  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && scheduled_at_ != nullptr) {
    delete scheduled_at_;
  }
  scheduled_at_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&state_) -
      reinterpret_cast<char*>(&id_)) + sizeof(state_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WorkOrder::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.WorkOrder.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp scheduled_at = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_scheduled_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 duration_minutes = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          duration_minutes_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Job jobs = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_jobs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.State state = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::State>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WorkOrder::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.WorkOrder)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.WorkOrder.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // .google.protobuf.Timestamp scheduled_at = 3;
  if (this->_internal_has_scheduled_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::scheduled_at(this), target, stream);
  }

  // int32 duration_minutes = 4;
  if (this->_internal_duration_minutes() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_duration_minutes(), target);
  }

  // repeated .carbon.rtc.Job jobs = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_jobs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_jobs(i), target, stream);
  }

  // .carbon.rtc.State state = 6;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.WorkOrder)
  return target;
}

size_t WorkOrder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.WorkOrder)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Job jobs = 5;
  total_size += 1UL * this->_internal_jobs_size();
  for (const auto& msg : this->jobs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .google.protobuf.Timestamp scheduled_at = 3;
  if (this->_internal_has_scheduled_at()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *scheduled_at_);
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  // int32 duration_minutes = 4;
  if (this->_internal_duration_minutes() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_duration_minutes());
  }

  // .carbon.rtc.State state = 6;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WorkOrder::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WorkOrder::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WorkOrder::GetClassData() const { return &_class_data_; }

void WorkOrder::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WorkOrder *>(to)->MergeFrom(
      static_cast<const WorkOrder &>(from));
}


void WorkOrder::MergeFrom(const WorkOrder& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.WorkOrder)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  jobs_.MergeFrom(from.jobs_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_scheduled_at()) {
    _internal_mutable_scheduled_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_scheduled_at());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_duration_minutes() != 0) {
    _internal_set_duration_minutes(from._internal_duration_minutes());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WorkOrder::CopyFrom(const WorkOrder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.WorkOrder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WorkOrder::IsInitialized() const {
  return true;
}

void WorkOrder::InternalSwap(WorkOrder* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  jobs_.InternalSwap(&other->jobs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WorkOrder, state_)
      + sizeof(WorkOrder::state_)
      - PROTOBUF_FIELD_OFFSET(WorkOrder, scheduled_at_)>(
          reinterpret_cast<char*>(&scheduled_at_),
          reinterpret_cast<char*>(&other->scheduled_at_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WorkOrder::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[23]);
}

// ===================================================================

class WorkOrderList::_Internal {
 public:
};

WorkOrderList::WorkOrderList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  work_orders_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.WorkOrderList)
}
WorkOrderList::WorkOrderList(const WorkOrderList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      work_orders_(from.work_orders_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.WorkOrderList)
}

inline void WorkOrderList::SharedCtor() {
}

WorkOrderList::~WorkOrderList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.WorkOrderList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WorkOrderList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void WorkOrderList::ArenaDtor(void* object) {
  WorkOrderList* _this = reinterpret_cast< WorkOrderList* >(object);
  (void)_this;
}
void WorkOrderList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WorkOrderList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WorkOrderList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.WorkOrderList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  work_orders_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WorkOrderList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.WorkOrder work_orders = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_work_orders(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WorkOrderList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.WorkOrderList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.WorkOrder work_orders = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_work_orders_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_work_orders(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.WorkOrderList)
  return target;
}

size_t WorkOrderList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.WorkOrderList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.WorkOrder work_orders = 1;
  total_size += 1UL * this->_internal_work_orders_size();
  for (const auto& msg : this->work_orders_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WorkOrderList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WorkOrderList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WorkOrderList::GetClassData() const { return &_class_data_; }

void WorkOrderList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WorkOrderList *>(to)->MergeFrom(
      static_cast<const WorkOrderList &>(from));
}


void WorkOrderList::MergeFrom(const WorkOrderList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.WorkOrderList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  work_orders_.MergeFrom(from.work_orders_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WorkOrderList::CopyFrom(const WorkOrderList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.WorkOrderList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WorkOrderList::IsInitialized() const {
  return true;
}

void WorkOrderList::InternalSwap(WorkOrderList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  work_orders_.InternalSwap(&other->work_orders_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WorkOrderList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[24]);
}

// ===================================================================

class ListWorkOrdersResponse::_Internal {
 public:
};

ListWorkOrdersResponse::ListWorkOrdersResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  work_orders_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListWorkOrdersResponse)
}
ListWorkOrdersResponse::ListWorkOrdersResponse(const ListWorkOrdersResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      work_orders_(from.work_orders_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListWorkOrdersResponse)
}

inline void ListWorkOrdersResponse::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListWorkOrdersResponse::~ListWorkOrdersResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListWorkOrdersResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListWorkOrdersResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListWorkOrdersResponse::ArenaDtor(void* object) {
  ListWorkOrdersResponse* _this = reinterpret_cast< ListWorkOrdersResponse* >(object);
  (void)_this;
}
void ListWorkOrdersResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListWorkOrdersResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListWorkOrdersResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListWorkOrdersResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  work_orders_.Clear();
  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListWorkOrdersResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListWorkOrdersResponse.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.WorkOrder work_orders = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_work_orders(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListWorkOrdersResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListWorkOrdersResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListWorkOrdersResponse.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  // repeated .carbon.rtc.WorkOrder work_orders = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_work_orders_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_work_orders(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListWorkOrdersResponse)
  return target;
}

size_t ListWorkOrdersResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListWorkOrdersResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.WorkOrder work_orders = 2;
  total_size += 1UL * this->_internal_work_orders_size();
  for (const auto& msg : this->work_orders_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListWorkOrdersResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListWorkOrdersResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListWorkOrdersResponse::GetClassData() const { return &_class_data_; }

void ListWorkOrdersResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListWorkOrdersResponse *>(to)->MergeFrom(
      static_cast<const ListWorkOrdersResponse &>(from));
}


void ListWorkOrdersResponse::MergeFrom(const ListWorkOrdersResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListWorkOrdersResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  work_orders_.MergeFrom(from.work_orders_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListWorkOrdersResponse::CopyFrom(const ListWorkOrdersResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListWorkOrdersResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListWorkOrdersResponse::IsInitialized() const {
  return true;
}

void ListWorkOrdersResponse::InternalSwap(ListWorkOrdersResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  work_orders_.InternalSwap(&other->work_orders_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListWorkOrdersResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[25]);
}

// ===================================================================

class Intervention::_Internal {
 public:
};

Intervention::Intervention(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.Intervention)
}
Intervention::Intervention(const Intervention& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  qualification_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    qualification_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_qualification().empty()) {
    qualification_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_qualification(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&id_)) + sizeof(priority_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.Intervention)
}

inline void Intervention::SharedCtor() {
qualification_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  qualification_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&priority_) -
    reinterpret_cast<char*>(&id_)) + sizeof(priority_));
}

Intervention::~Intervention() {
  // @@protoc_insertion_point(destructor:carbon.rtc.Intervention)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Intervention::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  qualification_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void Intervention::ArenaDtor(void* object) {
  Intervention* _this = reinterpret_cast< Intervention* >(object);
  (void)_this;
}
void Intervention::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Intervention::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Intervention::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.Intervention)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  qualification_.ClearToEmpty();
  description_.ClearToEmpty();
  robot_serial_.ClearToEmpty();
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&priority_) -
      reinterpret_cast<char*>(&id_)) + sizeof(priority_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Intervention::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 task_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          task_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string qualification = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_qualification();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Intervention.qualification"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Intervention.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.State state = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::State>(val));
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.Intervention.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 job_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          job_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.Intervention.InterventionCause cause = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_cause(static_cast<::carbon::rtc::Intervention_InterventionCause>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 priority = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          priority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Intervention::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.Intervention)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // uint64 task_id = 2;
  if (this->_internal_task_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_task_id(), target);
  }

  // string qualification = 3;
  if (!this->_internal_qualification().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_qualification().data(), static_cast<int>(this->_internal_qualification().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Intervention.qualification");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_qualification(), target);
  }

  // string description = 4;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Intervention.description");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_description(), target);
  }

  // .carbon.rtc.State state = 5;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_state(), target);
  }

  // string robot_serial = 6;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.Intervention.robot_serial");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_robot_serial(), target);
  }

  // uint64 job_id = 7;
  if (this->_internal_job_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(7, this->_internal_job_id(), target);
  }

  // .carbon.rtc.Intervention.InterventionCause cause = 8;
  if (this->_internal_cause() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_cause(), target);
  }

  // int32 priority = 9;
  if (this->_internal_priority() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(9, this->_internal_priority(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.Intervention)
  return target;
}

size_t Intervention::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.Intervention)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string qualification = 3;
  if (!this->_internal_qualification().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_qualification());
  }

  // string description = 4;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string robot_serial = 6;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  // uint64 task_id = 2;
  if (this->_internal_task_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_task_id());
  }

  // .carbon.rtc.State state = 5;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  // .carbon.rtc.Intervention.InterventionCause cause = 8;
  if (this->_internal_cause() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_cause());
  }

  // uint64 job_id = 7;
  if (this->_internal_job_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_job_id());
  }

  // int32 priority = 9;
  if (this->_internal_priority() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_priority());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Intervention::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Intervention::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Intervention::GetClassData() const { return &_class_data_; }

void Intervention::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Intervention *>(to)->MergeFrom(
      static_cast<const Intervention &>(from));
}


void Intervention::MergeFrom(const Intervention& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.Intervention)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_qualification().empty()) {
    _internal_set_qualification(from._internal_qualification());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_task_id() != 0) {
    _internal_set_task_id(from._internal_task_id());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  if (from._internal_cause() != 0) {
    _internal_set_cause(from._internal_cause());
  }
  if (from._internal_job_id() != 0) {
    _internal_set_job_id(from._internal_job_id());
  }
  if (from._internal_priority() != 0) {
    _internal_set_priority(from._internal_priority());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Intervention::CopyFrom(const Intervention& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.Intervention)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Intervention::IsInitialized() const {
  return true;
}

void Intervention::InternalSwap(Intervention* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &qualification_, lhs_arena,
      &other->qualification_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Intervention, priority_)
      + sizeof(Intervention::priority_)
      - PROTOBUF_FIELD_OFFSET(Intervention, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Intervention::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[26]);
}

// ===================================================================

class InterventionList::_Internal {
 public:
};

InterventionList::InterventionList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  intervention_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.InterventionList)
}
InterventionList::InterventionList(const InterventionList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      intervention_(from.intervention_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.InterventionList)
}

inline void InterventionList::SharedCtor() {
}

InterventionList::~InterventionList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.InterventionList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void InterventionList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void InterventionList::ArenaDtor(void* object) {
  InterventionList* _this = reinterpret_cast< InterventionList* >(object);
  (void)_this;
}
void InterventionList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void InterventionList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void InterventionList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.InterventionList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  intervention_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* InterventionList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.Intervention intervention = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_intervention(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* InterventionList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.InterventionList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.Intervention intervention = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_intervention_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_intervention(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.InterventionList)
  return target;
}

size_t InterventionList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.InterventionList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Intervention intervention = 1;
  total_size += 1UL * this->_internal_intervention_size();
  for (const auto& msg : this->intervention_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData InterventionList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    InterventionList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*InterventionList::GetClassData() const { return &_class_data_; }

void InterventionList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<InterventionList *>(to)->MergeFrom(
      static_cast<const InterventionList &>(from));
}


void InterventionList::MergeFrom(const InterventionList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.InterventionList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  intervention_.MergeFrom(from.intervention_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void InterventionList::CopyFrom(const InterventionList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.InterventionList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InterventionList::IsInitialized() const {
  return true;
}

void InterventionList::InternalSwap(InterventionList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  intervention_.InternalSwap(&other->intervention_);
}

::PROTOBUF_NAMESPACE_ID::Metadata InterventionList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[27]);
}

// ===================================================================

class ListInterventionsRequest::_Internal {
 public:
};

ListInterventionsRequest::ListInterventionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListInterventionsRequest)
}
ListInterventionsRequest::ListInterventionsRequest(const ListInterventionsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  page_size_ = from.page_size_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListInterventionsRequest)
}

inline void ListInterventionsRequest::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
page_size_ = 0;
}

ListInterventionsRequest::~ListInterventionsRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListInterventionsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListInterventionsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListInterventionsRequest::ArenaDtor(void* object) {
  ListInterventionsRequest* _this = reinterpret_cast< ListInterventionsRequest* >(object);
  (void)_this;
}
void ListInterventionsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListInterventionsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListInterventionsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListInterventionsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  page_token_.ClearToEmpty();
  page_size_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListInterventionsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 page_size = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          page_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string page_token = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListInterventionsRequest.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListInterventionsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListInterventionsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_page_size(), target);
  }

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListInterventionsRequest.page_token");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_page_token(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListInterventionsRequest)
  return target;
}

size_t ListInterventionsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListInterventionsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_page_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListInterventionsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListInterventionsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListInterventionsRequest::GetClassData() const { return &_class_data_; }

void ListInterventionsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListInterventionsRequest *>(to)->MergeFrom(
      static_cast<const ListInterventionsRequest &>(from));
}


void ListInterventionsRequest::MergeFrom(const ListInterventionsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListInterventionsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  if (from._internal_page_size() != 0) {
    _internal_set_page_size(from._internal_page_size());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListInterventionsRequest::CopyFrom(const ListInterventionsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListInterventionsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListInterventionsRequest::IsInitialized() const {
  return true;
}

void ListInterventionsRequest::InternalSwap(ListInterventionsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
  swap(page_size_, other->page_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ListInterventionsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[28]);
}

// ===================================================================

class ListInterventionsResponse::_Internal {
 public:
};

ListInterventionsResponse::ListInterventionsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  interventions_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListInterventionsResponse)
}
ListInterventionsResponse::ListInterventionsResponse(const ListInterventionsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      interventions_(from.interventions_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListInterventionsResponse)
}

inline void ListInterventionsResponse::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListInterventionsResponse::~ListInterventionsResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListInterventionsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListInterventionsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListInterventionsResponse::ArenaDtor(void* object) {
  ListInterventionsResponse* _this = reinterpret_cast< ListInterventionsResponse* >(object);
  (void)_this;
}
void ListInterventionsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListInterventionsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListInterventionsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListInterventionsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  interventions_.Clear();
  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListInterventionsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListInterventionsResponse.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.Intervention interventions = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_interventions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListInterventionsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListInterventionsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListInterventionsResponse.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  // repeated .carbon.rtc.Intervention interventions = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_interventions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_interventions(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListInterventionsResponse)
  return target;
}

size_t ListInterventionsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListInterventionsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.Intervention interventions = 2;
  total_size += 1UL * this->_internal_interventions_size();
  for (const auto& msg : this->interventions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListInterventionsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListInterventionsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListInterventionsResponse::GetClassData() const { return &_class_data_; }

void ListInterventionsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListInterventionsResponse *>(to)->MergeFrom(
      static_cast<const ListInterventionsResponse &>(from));
}


void ListInterventionsResponse::MergeFrom(const ListInterventionsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListInterventionsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  interventions_.MergeFrom(from.interventions_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListInterventionsResponse::CopyFrom(const ListInterventionsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListInterventionsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListInterventionsResponse::IsInitialized() const {
  return true;
}

void ListInterventionsResponse::InternalSwap(ListInterventionsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  interventions_.InternalSwap(&other->interventions_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListInterventionsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[29]);
}

// ===================================================================

class CreateInterventionRequest::_Internal {
 public:
  static const ::carbon::rtc::Intervention& intervention(const CreateInterventionRequest* msg);
};

const ::carbon::rtc::Intervention&
CreateInterventionRequest::_Internal::intervention(const CreateInterventionRequest* msg) {
  return *msg->intervention_;
}
CreateInterventionRequest::CreateInterventionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.CreateInterventionRequest)
}
CreateInterventionRequest::CreateInterventionRequest(const CreateInterventionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_intervention()) {
    intervention_ = new ::carbon::rtc::Intervention(*from.intervention_);
  } else {
    intervention_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.CreateInterventionRequest)
}

inline void CreateInterventionRequest::SharedCtor() {
intervention_ = nullptr;
}

CreateInterventionRequest::~CreateInterventionRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.CreateInterventionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CreateInterventionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete intervention_;
}

void CreateInterventionRequest::ArenaDtor(void* object) {
  CreateInterventionRequest* _this = reinterpret_cast< CreateInterventionRequest* >(object);
  (void)_this;
}
void CreateInterventionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateInterventionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateInterventionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.CreateInterventionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && intervention_ != nullptr) {
    delete intervention_;
  }
  intervention_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CreateInterventionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Intervention intervention = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_intervention(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateInterventionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.CreateInterventionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Intervention intervention = 1;
  if (this->_internal_has_intervention()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::intervention(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.CreateInterventionRequest)
  return target;
}

size_t CreateInterventionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.CreateInterventionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Intervention intervention = 1;
  if (this->_internal_has_intervention()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *intervention_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CreateInterventionRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CreateInterventionRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CreateInterventionRequest::GetClassData() const { return &_class_data_; }

void CreateInterventionRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CreateInterventionRequest *>(to)->MergeFrom(
      static_cast<const CreateInterventionRequest &>(from));
}


void CreateInterventionRequest::MergeFrom(const CreateInterventionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.CreateInterventionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_intervention()) {
    _internal_mutable_intervention()->::carbon::rtc::Intervention::MergeFrom(from._internal_intervention());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CreateInterventionRequest::CopyFrom(const CreateInterventionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.CreateInterventionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateInterventionRequest::IsInitialized() const {
  return true;
}

void CreateInterventionRequest::InternalSwap(CreateInterventionRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(intervention_, other->intervention_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CreateInterventionRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[30]);
}

// ===================================================================

class CreateInterventionResponse::_Internal {
 public:
  static const ::carbon::rtc::Intervention& intervention(const CreateInterventionResponse* msg);
};

const ::carbon::rtc::Intervention&
CreateInterventionResponse::_Internal::intervention(const CreateInterventionResponse* msg) {
  return *msg->intervention_;
}
CreateInterventionResponse::CreateInterventionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.CreateInterventionResponse)
}
CreateInterventionResponse::CreateInterventionResponse(const CreateInterventionResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_intervention()) {
    intervention_ = new ::carbon::rtc::Intervention(*from.intervention_);
  } else {
    intervention_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.CreateInterventionResponse)
}

inline void CreateInterventionResponse::SharedCtor() {
intervention_ = nullptr;
}

CreateInterventionResponse::~CreateInterventionResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.CreateInterventionResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CreateInterventionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete intervention_;
}

void CreateInterventionResponse::ArenaDtor(void* object) {
  CreateInterventionResponse* _this = reinterpret_cast< CreateInterventionResponse* >(object);
  (void)_this;
}
void CreateInterventionResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateInterventionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateInterventionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.CreateInterventionResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && intervention_ != nullptr) {
    delete intervention_;
  }
  intervention_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CreateInterventionResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Intervention intervention = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_intervention(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateInterventionResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.CreateInterventionResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Intervention intervention = 1;
  if (this->_internal_has_intervention()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::intervention(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.CreateInterventionResponse)
  return target;
}

size_t CreateInterventionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.CreateInterventionResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Intervention intervention = 1;
  if (this->_internal_has_intervention()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *intervention_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CreateInterventionResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CreateInterventionResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CreateInterventionResponse::GetClassData() const { return &_class_data_; }

void CreateInterventionResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CreateInterventionResponse *>(to)->MergeFrom(
      static_cast<const CreateInterventionResponse &>(from));
}


void CreateInterventionResponse::MergeFrom(const CreateInterventionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.CreateInterventionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_intervention()) {
    _internal_mutable_intervention()->::carbon::rtc::Intervention::MergeFrom(from._internal_intervention());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CreateInterventionResponse::CopyFrom(const CreateInterventionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.CreateInterventionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateInterventionResponse::IsInitialized() const {
  return true;
}

void CreateInterventionResponse::InternalSwap(CreateInterventionResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(intervention_, other->intervention_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CreateInterventionResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[31]);
}

// ===================================================================

class GetActiveTaskRequest::_Internal {
 public:
  static const ::carbon::geo::Point& current_location(const GetActiveTaskRequest* msg);
};

const ::carbon::geo::Point&
GetActiveTaskRequest::_Internal::current_location(const GetActiveTaskRequest* msg) {
  return *msg->current_location_;
}
void GetActiveTaskRequest::clear_current_location() {
  if (GetArenaForAllocation() == nullptr && current_location_ != nullptr) {
    delete current_location_;
  }
  current_location_ = nullptr;
}
GetActiveTaskRequest::GetActiveTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetActiveTaskRequest)
}
GetActiveTaskRequest::GetActiveTaskRequest(const GetActiveTaskRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_current_location()) {
    current_location_ = new ::carbon::geo::Point(*from.current_location_);
  } else {
    current_location_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetActiveTaskRequest)
}

inline void GetActiveTaskRequest::SharedCtor() {
current_location_ = nullptr;
}

GetActiveTaskRequest::~GetActiveTaskRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetActiveTaskRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetActiveTaskRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete current_location_;
}

void GetActiveTaskRequest::ArenaDtor(void* object) {
  GetActiveTaskRequest* _this = reinterpret_cast< GetActiveTaskRequest* >(object);
  (void)_this;
}
void GetActiveTaskRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetActiveTaskRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetActiveTaskRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetActiveTaskRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && current_location_ != nullptr) {
    delete current_location_;
  }
  current_location_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetActiveTaskRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point current_location = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_current_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetActiveTaskRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetActiveTaskRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point current_location = 2;
  if (this->_internal_has_current_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::current_location(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetActiveTaskRequest)
  return target;
}

size_t GetActiveTaskRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetActiveTaskRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point current_location = 2;
  if (this->_internal_has_current_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_location_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetActiveTaskRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetActiveTaskRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetActiveTaskRequest::GetClassData() const { return &_class_data_; }

void GetActiveTaskRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetActiveTaskRequest *>(to)->MergeFrom(
      static_cast<const GetActiveTaskRequest &>(from));
}


void GetActiveTaskRequest::MergeFrom(const GetActiveTaskRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetActiveTaskRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_current_location()) {
    _internal_mutable_current_location()->::carbon::geo::Point::MergeFrom(from._internal_current_location());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetActiveTaskRequest::CopyFrom(const GetActiveTaskRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetActiveTaskRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetActiveTaskRequest::IsInitialized() const {
  return true;
}

void GetActiveTaskRequest::InternalSwap(GetActiveTaskRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(current_location_, other->current_location_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetActiveTaskRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[32]);
}

// ===================================================================

class GetActiveTaskResponse::_Internal {
 public:
  static const ::carbon::rtc::Task& task(const GetActiveTaskResponse* msg);
};

const ::carbon::rtc::Task&
GetActiveTaskResponse::_Internal::task(const GetActiveTaskResponse* msg) {
  return *msg->task_;
}
GetActiveTaskResponse::GetActiveTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetActiveTaskResponse)
}
GetActiveTaskResponse::GetActiveTaskResponse(const GetActiveTaskResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_task()) {
    task_ = new ::carbon::rtc::Task(*from.task_);
  } else {
    task_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetActiveTaskResponse)
}

inline void GetActiveTaskResponse::SharedCtor() {
task_ = nullptr;
}

GetActiveTaskResponse::~GetActiveTaskResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetActiveTaskResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetActiveTaskResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete task_;
}

void GetActiveTaskResponse::ArenaDtor(void* object) {
  GetActiveTaskResponse* _this = reinterpret_cast< GetActiveTaskResponse* >(object);
  (void)_this;
}
void GetActiveTaskResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetActiveTaskResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetActiveTaskResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetActiveTaskResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && task_ != nullptr) {
    delete task_;
  }
  task_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetActiveTaskResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Task task = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_task(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetActiveTaskResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetActiveTaskResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::task(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetActiveTaskResponse)
  return target;
}

size_t GetActiveTaskResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetActiveTaskResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *task_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetActiveTaskResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetActiveTaskResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetActiveTaskResponse::GetClassData() const { return &_class_data_; }

void GetActiveTaskResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetActiveTaskResponse *>(to)->MergeFrom(
      static_cast<const GetActiveTaskResponse &>(from));
}


void GetActiveTaskResponse::MergeFrom(const GetActiveTaskResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetActiveTaskResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_task()) {
    _internal_mutable_task()->::carbon::rtc::Task::MergeFrom(from._internal_task());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetActiveTaskResponse::CopyFrom(const GetActiveTaskResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetActiveTaskResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetActiveTaskResponse::IsInitialized() const {
  return true;
}

void GetActiveTaskResponse::InternalSwap(GetActiveTaskResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(task_, other->task_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetActiveTaskResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[33]);
}

// ===================================================================

class GetTaskRequest::_Internal {
 public:
};

GetTaskRequest::GetTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetTaskRequest)
}
GetTaskRequest::GetTaskRequest(const GetTaskRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_id_ = from.task_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetTaskRequest)
}

inline void GetTaskRequest::SharedCtor() {
task_id_ = uint64_t{0u};
}

GetTaskRequest::~GetTaskRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetTaskRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetTaskRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetTaskRequest::ArenaDtor(void* object) {
  GetTaskRequest* _this = reinterpret_cast< GetTaskRequest* >(object);
  (void)_this;
}
void GetTaskRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetTaskRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetTaskRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetTaskRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  task_id_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetTaskRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 task_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          task_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetTaskRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetTaskRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_task_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetTaskRequest)
  return target;
}

size_t GetTaskRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetTaskRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_task_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetTaskRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetTaskRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetTaskRequest::GetClassData() const { return &_class_data_; }

void GetTaskRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetTaskRequest *>(to)->MergeFrom(
      static_cast<const GetTaskRequest &>(from));
}


void GetTaskRequest::MergeFrom(const GetTaskRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetTaskRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_task_id() != 0) {
    _internal_set_task_id(from._internal_task_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetTaskRequest::CopyFrom(const GetTaskRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetTaskRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTaskRequest::IsInitialized() const {
  return true;
}

void GetTaskRequest::InternalSwap(GetTaskRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(task_id_, other->task_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetTaskRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[34]);
}

// ===================================================================

class GetTaskResponse::_Internal {
 public:
  static const ::carbon::rtc::Task& task(const GetTaskResponse* msg);
};

const ::carbon::rtc::Task&
GetTaskResponse::_Internal::task(const GetTaskResponse* msg) {
  return *msg->task_;
}
GetTaskResponse::GetTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetTaskResponse)
}
GetTaskResponse::GetTaskResponse(const GetTaskResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_task()) {
    task_ = new ::carbon::rtc::Task(*from.task_);
  } else {
    task_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetTaskResponse)
}

inline void GetTaskResponse::SharedCtor() {
task_ = nullptr;
}

GetTaskResponse::~GetTaskResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetTaskResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetTaskResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete task_;
}

void GetTaskResponse::ArenaDtor(void* object) {
  GetTaskResponse* _this = reinterpret_cast< GetTaskResponse* >(object);
  (void)_this;
}
void GetTaskResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetTaskResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetTaskResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetTaskResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && task_ != nullptr) {
    delete task_;
  }
  task_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetTaskResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Task task = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_task(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetTaskResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetTaskResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::task(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetTaskResponse)
  return target;
}

size_t GetTaskResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetTaskResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *task_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetTaskResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetTaskResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetTaskResponse::GetClassData() const { return &_class_data_; }

void GetTaskResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetTaskResponse *>(to)->MergeFrom(
      static_cast<const GetTaskResponse &>(from));
}


void GetTaskResponse::MergeFrom(const GetTaskResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetTaskResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_task()) {
    _internal_mutable_task()->::carbon::rtc::Task::MergeFrom(from._internal_task());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetTaskResponse::CopyFrom(const GetTaskResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetTaskResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTaskResponse::IsInitialized() const {
  return true;
}

void GetTaskResponse::InternalSwap(GetTaskResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(task_, other->task_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetTaskResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[35]);
}

// ===================================================================

class UpdateTaskRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<UpdateTaskRequest>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& started_at(const UpdateTaskRequest* msg);
  static void set_has_started_at(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::carbon::geo::Point& start_location(const UpdateTaskRequest* msg);
  static void set_has_start_location(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_start_heading(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& ended_at(const UpdateTaskRequest* msg);
  static void set_has_ended_at(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::carbon::geo::Point& end_location(const UpdateTaskRequest* msg);
  static void set_has_end_location(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_end_heading(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_status_info(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
UpdateTaskRequest::_Internal::started_at(const UpdateTaskRequest* msg) {
  return *msg->started_at_;
}
const ::carbon::geo::Point&
UpdateTaskRequest::_Internal::start_location(const UpdateTaskRequest* msg) {
  return *msg->start_location_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
UpdateTaskRequest::_Internal::ended_at(const UpdateTaskRequest* msg) {
  return *msg->ended_at_;
}
const ::carbon::geo::Point&
UpdateTaskRequest::_Internal::end_location(const UpdateTaskRequest* msg) {
  return *msg->end_location_;
}
void UpdateTaskRequest::clear_started_at() {
  if (started_at_ != nullptr) started_at_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
void UpdateTaskRequest::clear_start_location() {
  if (start_location_ != nullptr) start_location_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
void UpdateTaskRequest::clear_ended_at() {
  if (ended_at_ != nullptr) ended_at_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
void UpdateTaskRequest::clear_end_location() {
  if (end_location_ != nullptr) end_location_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
UpdateTaskRequest::UpdateTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.UpdateTaskRequest)
}
UpdateTaskRequest::UpdateTaskRequest(const UpdateTaskRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  status_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_status_info()) {
    status_info_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_info(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_started_at()) {
    started_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.started_at_);
  } else {
    started_at_ = nullptr;
  }
  if (from._internal_has_start_location()) {
    start_location_ = new ::carbon::geo::Point(*from.start_location_);
  } else {
    start_location_ = nullptr;
  }
  if (from._internal_has_ended_at()) {
    ended_at_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.ended_at_);
  } else {
    ended_at_ = nullptr;
  }
  if (from._internal_has_end_location()) {
    end_location_ = new ::carbon::geo::Point(*from.end_location_);
  } else {
    end_location_ = nullptr;
  }
  ::memcpy(&task_id_, &from.task_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&task_id_)) + sizeof(state_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.UpdateTaskRequest)
}

inline void UpdateTaskRequest::SharedCtor() {
status_info_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_info_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&started_at_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&started_at_)) + sizeof(state_));
}

UpdateTaskRequest::~UpdateTaskRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.UpdateTaskRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpdateTaskRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  status_info_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete started_at_;
  if (this != internal_default_instance()) delete start_location_;
  if (this != internal_default_instance()) delete ended_at_;
  if (this != internal_default_instance()) delete end_location_;
}

void UpdateTaskRequest::ArenaDtor(void* object) {
  UpdateTaskRequest* _this = reinterpret_cast< UpdateTaskRequest* >(object);
  (void)_this;
}
void UpdateTaskRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpdateTaskRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpdateTaskRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.UpdateTaskRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      status_info_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(started_at_ != nullptr);
      started_at_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(start_location_ != nullptr);
      start_location_->Clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(ended_at_ != nullptr);
      ended_at_->Clear();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(end_location_ != nullptr);
      end_location_->Clear();
    }
  }
  task_id_ = uint64_t{0u};
  if (cached_has_bits & 0x000000e0u) {
    ::memset(&start_heading_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&state_) -
        reinterpret_cast<char*>(&start_heading_)) + sizeof(state_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpdateTaskRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 task_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          task_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .carbon.rtc.State state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::State>(val));
        } else
          goto handle_unusual;
        continue;
      // optional .google.protobuf.Timestamp started_at = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_started_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .carbon.geo.Point start_location = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional double start_heading = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          _Internal::set_has_start_heading(&has_bits);
          start_heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // optional .google.protobuf.Timestamp ended_at = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_ended_at(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .carbon.geo.Point end_location = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional double end_heading = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 65)) {
          _Internal::set_has_end_heading(&has_bits);
          end_heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // optional string status_info = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_status_info();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.UpdateTaskRequest.status_info"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpdateTaskRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.UpdateTaskRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_task_id(), target);
  }

  // optional .carbon.rtc.State state = 2;
  if (_internal_has_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_state(), target);
  }

  // optional .google.protobuf.Timestamp started_at = 3;
  if (_internal_has_started_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::started_at(this), target, stream);
  }

  // optional .carbon.geo.Point start_location = 4;
  if (_internal_has_start_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::start_location(this), target, stream);
  }

  // optional double start_heading = 5;
  if (_internal_has_start_heading()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_start_heading(), target);
  }

  // optional .google.protobuf.Timestamp ended_at = 6;
  if (_internal_has_ended_at()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::ended_at(this), target, stream);
  }

  // optional .carbon.geo.Point end_location = 7;
  if (_internal_has_end_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::end_location(this), target, stream);
  }

  // optional double end_heading = 8;
  if (_internal_has_end_heading()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(8, this->_internal_end_heading(), target);
  }

  // optional string status_info = 9;
  if (_internal_has_status_info()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_info().data(), static_cast<int>(this->_internal_status_info().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.UpdateTaskRequest.status_info");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_status_info(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.UpdateTaskRequest)
  return target;
}

size_t UpdateTaskRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.UpdateTaskRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional string status_info = 9;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_status_info());
    }

    // optional .google.protobuf.Timestamp started_at = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *started_at_);
    }

    // optional .carbon.geo.Point start_location = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *start_location_);
    }

    // optional .google.protobuf.Timestamp ended_at = 6;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *ended_at_);
    }

    // optional .carbon.geo.Point end_location = 7;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *end_location_);
    }

  }
  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_task_id());
  }

  if (cached_has_bits & 0x000000e0u) {
    // optional double start_heading = 5;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 + 8;
    }

    // optional double end_heading = 8;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 + 8;
    }

    // optional .carbon.rtc.State state = 2;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpdateTaskRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpdateTaskRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpdateTaskRequest::GetClassData() const { return &_class_data_; }

void UpdateTaskRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpdateTaskRequest *>(to)->MergeFrom(
      static_cast<const UpdateTaskRequest &>(from));
}


void UpdateTaskRequest::MergeFrom(const UpdateTaskRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.UpdateTaskRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_status_info(from._internal_status_info());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_started_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_started_at());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_start_location()->::carbon::geo::Point::MergeFrom(from._internal_start_location());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_ended_at()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_ended_at());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_end_location()->::carbon::geo::Point::MergeFrom(from._internal_end_location());
    }
  }
  if (from._internal_task_id() != 0) {
    _internal_set_task_id(from._internal_task_id());
  }
  if (cached_has_bits & 0x000000e0u) {
    if (cached_has_bits & 0x00000020u) {
      start_heading_ = from.start_heading_;
    }
    if (cached_has_bits & 0x00000040u) {
      end_heading_ = from.end_heading_;
    }
    if (cached_has_bits & 0x00000080u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpdateTaskRequest::CopyFrom(const UpdateTaskRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.UpdateTaskRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateTaskRequest::IsInitialized() const {
  return true;
}

void UpdateTaskRequest::InternalSwap(UpdateTaskRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_info_, lhs_arena,
      &other->status_info_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UpdateTaskRequest, state_)
      + sizeof(UpdateTaskRequest::state_)
      - PROTOBUF_FIELD_OFFSET(UpdateTaskRequest, started_at_)>(
          reinterpret_cast<char*>(&started_at_),
          reinterpret_cast<char*>(&other->started_at_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UpdateTaskRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[36]);
}

// ===================================================================

class UpdateTaskResponse::_Internal {
 public:
  static const ::carbon::rtc::Task& task(const UpdateTaskResponse* msg);
};

const ::carbon::rtc::Task&
UpdateTaskResponse::_Internal::task(const UpdateTaskResponse* msg) {
  return *msg->task_;
}
UpdateTaskResponse::UpdateTaskResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.UpdateTaskResponse)
}
UpdateTaskResponse::UpdateTaskResponse(const UpdateTaskResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_task()) {
    task_ = new ::carbon::rtc::Task(*from.task_);
  } else {
    task_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.UpdateTaskResponse)
}

inline void UpdateTaskResponse::SharedCtor() {
task_ = nullptr;
}

UpdateTaskResponse::~UpdateTaskResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.UpdateTaskResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpdateTaskResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete task_;
}

void UpdateTaskResponse::ArenaDtor(void* object) {
  UpdateTaskResponse* _this = reinterpret_cast< UpdateTaskResponse* >(object);
  (void)_this;
}
void UpdateTaskResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpdateTaskResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpdateTaskResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.UpdateTaskResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && task_ != nullptr) {
    delete task_;
  }
  task_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpdateTaskResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.Task task = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_task(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpdateTaskResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.UpdateTaskResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::task(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.UpdateTaskResponse)
  return target;
}

size_t UpdateTaskResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.UpdateTaskResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.Task task = 1;
  if (this->_internal_has_task()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *task_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpdateTaskResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpdateTaskResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpdateTaskResponse::GetClassData() const { return &_class_data_; }

void UpdateTaskResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpdateTaskResponse *>(to)->MergeFrom(
      static_cast<const UpdateTaskResponse &>(from));
}


void UpdateTaskResponse::MergeFrom(const UpdateTaskResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.UpdateTaskResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_task()) {
    _internal_mutable_task()->::carbon::rtc::Task::MergeFrom(from._internal_task());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpdateTaskResponse::CopyFrom(const UpdateTaskResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.UpdateTaskResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateTaskResponse::IsInitialized() const {
  return true;
}

void UpdateTaskResponse::InternalSwap(UpdateTaskResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(task_, other->task_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UpdateTaskResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fjobs_2eproto_getter, &descriptor_table_proto_2frtc_2fjobs_2eproto_once,
      file_level_metadata_proto_2frtc_2fjobs_2eproto[37]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::rtc::Objective* Arena::CreateMaybeMessage< ::carbon::rtc::Objective >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::Objective >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ObjectiveList* Arena::CreateMaybeMessage< ::carbon::rtc::ObjectiveList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ObjectiveList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListObjectivesResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListObjectivesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListObjectivesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetNextActiveObjectiveRequest* Arena::CreateMaybeMessage< ::carbon::rtc::GetNextActiveObjectiveRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetNextActiveObjectiveRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetNextActiveObjectiveResponse* Arena::CreateMaybeMessage< ::carbon::rtc::GetNextActiveObjectiveResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetNextActiveObjectiveResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::Task* Arena::CreateMaybeMessage< ::carbon::rtc::Task >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::Task >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::StopAutonomyTask* Arena::CreateMaybeMessage< ::carbon::rtc::StopAutonomyTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::StopAutonomyTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::LaserWeedTask* Arena::CreateMaybeMessage< ::carbon::rtc::LaserWeedTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::LaserWeedTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SequenceTask* Arena::CreateMaybeMessage< ::carbon::rtc::SequenceTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SequenceTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ManualTask* Arena::CreateMaybeMessage< ::carbon::rtc::ManualTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ManualTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GoToAndFaceTask* Arena::CreateMaybeMessage< ::carbon::rtc::GoToAndFaceTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GoToAndFaceTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GoToReversiblePathTask* Arena::CreateMaybeMessage< ::carbon::rtc::GoToReversiblePathTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GoToReversiblePathTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::FollowPathTask* Arena::CreateMaybeMessage< ::carbon::rtc::FollowPathTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::FollowPathTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SpeedSetting* Arena::CreateMaybeMessage< ::carbon::rtc::SpeedSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SpeedSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SetTractorStateTask* Arena::CreateMaybeMessage< ::carbon::rtc::SetTractorStateTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SetTractorStateTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::TractorState* Arena::CreateMaybeMessage< ::carbon::rtc::TractorState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::TractorState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::HitchState* Arena::CreateMaybeMessage< ::carbon::rtc::HitchState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::HitchState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::TaskList* Arena::CreateMaybeMessage< ::carbon::rtc::TaskList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::TaskList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListTasksResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListTasksResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListTasksResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SpatialPathTolerance* Arena::CreateMaybeMessage< ::carbon::rtc::SpatialPathTolerance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SpatialPathTolerance >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::Job* Arena::CreateMaybeMessage< ::carbon::rtc::Job >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::Job >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::JobList* Arena::CreateMaybeMessage< ::carbon::rtc::JobList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::JobList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListJobsResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListJobsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListJobsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::WorkOrder* Arena::CreateMaybeMessage< ::carbon::rtc::WorkOrder >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::WorkOrder >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::WorkOrderList* Arena::CreateMaybeMessage< ::carbon::rtc::WorkOrderList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::WorkOrderList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListWorkOrdersResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListWorkOrdersResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListWorkOrdersResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::Intervention* Arena::CreateMaybeMessage< ::carbon::rtc::Intervention >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::Intervention >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::InterventionList* Arena::CreateMaybeMessage< ::carbon::rtc::InterventionList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::InterventionList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListInterventionsRequest* Arena::CreateMaybeMessage< ::carbon::rtc::ListInterventionsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListInterventionsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListInterventionsResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListInterventionsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListInterventionsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::CreateInterventionRequest* Arena::CreateMaybeMessage< ::carbon::rtc::CreateInterventionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::CreateInterventionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::CreateInterventionResponse* Arena::CreateMaybeMessage< ::carbon::rtc::CreateInterventionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::CreateInterventionResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetActiveTaskRequest* Arena::CreateMaybeMessage< ::carbon::rtc::GetActiveTaskRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetActiveTaskRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetActiveTaskResponse* Arena::CreateMaybeMessage< ::carbon::rtc::GetActiveTaskResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetActiveTaskResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetTaskRequest* Arena::CreateMaybeMessage< ::carbon::rtc::GetTaskRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetTaskRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetTaskResponse* Arena::CreateMaybeMessage< ::carbon::rtc::GetTaskResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetTaskResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::UpdateTaskRequest* Arena::CreateMaybeMessage< ::carbon::rtc::UpdateTaskRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::UpdateTaskRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::UpdateTaskResponse* Arena::CreateMaybeMessage< ::carbon::rtc::UpdateTaskResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::UpdateTaskResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
