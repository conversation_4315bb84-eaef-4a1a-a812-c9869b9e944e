# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/rtc/jobs.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from generated.proto.geo import geo_pb2 as proto_dot_geo_dot_geo__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/rtc/jobs.proto',
  package='carbon.rtc',
  syntax='proto3',
  serialized_options=b'Z\tproto/rtc',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x14proto/rtc/jobs.proto\x12\ncarbon.rtc\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x13proto/geo/geo.proto\"\x85\x02\n\tObjective\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x18\n\x10progress_percent\x18\x04 \x01(\x05\x12\x10\n\x08priority\x18\x05 \x01(\x05\x12\x31\n\x04type\x18\x06 \x01(\x0e\x32#.carbon.rtc.Objective.ObjectiveType\x12%\n\x04\x64\x61ta\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\"C\n\rObjectiveType\x12\x1e\n\x1aOBJECTIVE_TYPE_UNSPECIFIED\x10\x00\x12\x12\n\x0eLASER_WEED_ROW\x10\x01\":\n\rObjectiveList\x12)\n\nobjectives\x18\x01 \x03(\x0b\x32\x15.carbon.rtc.Objective\"W\n\x16ListObjectivesResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12)\n\nobjectives\x18\x02 \x03(\x0b\x32\x15.carbon.rtc.Objective\"5\n\x1dGetNextActiveObjectiveRequest\x12\x14\n\x0cobjective_id\x18\x01 \x01(\x04\"J\n\x1eGetNextActiveObjectiveResponse\x12(\n\tobjective\x18\x02 \x01(\x0b\x32\x15.carbon.rtc.Objective\"\x86\x07\n\x04Task\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12.\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nded_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x11\x65xpected_duration\x18\x05 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x13\n\x0bstatus_info\x18\x06 \x01(\t\x12\x38\n\x16\x65xpected_tractor_state\x18\x07 \x03(\x0b\x32\x18.carbon.rtc.TractorState\x12 \n\x05state\x18\x08 \x01(\x0e\x32\x11.carbon.rtc.State\x12\x10\n\x08priority\x18\t \x01(\x05\x12\x14\n\x0cobjective_id\x18\x10 \x01(\x04\x12)\n\x0estart_location\x18\x12 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x15\n\rstart_heading\x18\x13 \x01(\x01\x12\'\n\x0c\x65nd_location\x18\x14 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x13\n\x0b\x65nd_heading\x18\x15 \x01(\x01\x12,\n\x08sequence\x18\n \x01(\x0b\x32\x18.carbon.rtc.SequenceTaskH\x00\x12(\n\x06manual\x18\x0b \x01(\x0b\x32\x16.carbon.rtc.ManualTaskH\x00\x12\x35\n\x0ego_to_and_face\x18\x0c \x01(\x0b\x32\x1b.carbon.rtc.GoToAndFaceTaskH\x00\x12\x31\n\x0b\x66ollow_path\x18\r \x01(\x0b\x32\x1a.carbon.rtc.FollowPathTaskH\x00\x12\x38\n\rtractor_state\x18\x0e \x01(\x0b\x32\x1f.carbon.rtc.SetTractorStateTaskH\x00\x12/\n\nlaser_weed\x18\x0f \x01(\x0b\x32\x19.carbon.rtc.LaserWeedTaskH\x00\x12\x35\n\rstop_autonomy\x18\x11 \x01(\x0b\x32\x1c.carbon.rtc.StopAutonomyTaskH\x00\x12\x43\n\x15go_to_reversible_path\x18\x16 \x01(\x0b\x32\".carbon.rtc.GoToReversiblePathTaskH\x00\x42\x0e\n\x0ctask_details\"\x12\n\x10StopAutonomyTask\"\xca\x01\n\rLaserWeedTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x1a\n\x12path_is_reversible\x18\x02 \x01(\x08\x12\x17\n\x0fweeding_enabled\x18\x03 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x04 \x01(\x08\x12\x0e\n\x06manual\x18\x05 \x01(\x08\x12\x34\n\ntolerances\x18\x06 \x01(\x0b\x32 .carbon.rtc.SpatialPathTolerance\"?\n\x0cSequenceTask\x12\x1f\n\x05items\x18\x01 \x03(\x0b\x32\x10.carbon.rtc.Task\x12\x0e\n\x06\x61tomic\x18\x02 \x01(\x08\"\"\n\nManualTask\x12\x14\n\x0cinstructions\x18\x01 \x01(\t\"D\n\x0fGoToAndFaceTask\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x0f\n\x07heading\x18\x02 \x01(\x01\"t\n\x16GoToReversiblePathTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x34\n\ntolerances\x18\x02 \x01(\x0b\x32 .carbon.rtc.SpatialPathTolerance\"{\n\x0e\x46ollowPathTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\'\n\x05speed\x18\x02 \x01(\x0b\x32\x18.carbon.rtc.SpeedSetting\x12\x1a\n\x12stop_on_completion\x18\x03 \x01(\x08\"\xa5\x01\n\x0cSpeedSetting\x12\x16\n\x0c\x63onstant_mph\x18\x01 \x01(\x01H\x00\x12<\n\x1aremote_operator_controlled\x18\x02 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x12\x36\n\x14implement_controlled\x18\x03 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x42\x07\n\x05speed\">\n\x13SetTractorStateTask\x12\'\n\x05state\x18\x01 \x03(\x0b\x32\x18.carbon.rtc.TractorState\"\xcd\x01\n\x0cTractorState\x12-\n\x04gear\x18\x01 \x01(\x0e\x32\x1d.carbon.rtc.TractorState.GearH\x00\x12\'\n\x05hitch\x18\x02 \x01(\x0b\x32\x16.carbon.rtc.HitchStateH\x00\"\\\n\x04Gear\x12\x14\n\x10GEAR_UNSPECIFIED\x10\x00\x12\x08\n\x04PARK\x10\x01\x12\x0b\n\x07REVERSE\x10\x02\x12\x0b\n\x07NEUTRAL\x10\x03\x12\x0b\n\x07\x46ORWARD\x10\x04\x12\r\n\tPOWERZERO\x10\x05\x42\x07\n\x05state\"\xa9\x01\n\nHitchState\x12\x36\n\x07\x63ommand\x18\x01 \x01(\x0e\x32#.carbon.rtc.HitchState.HitchCommandH\x00\x12\x12\n\x08position\x18\x02 \x01(\x01H\x00\"F\n\x0cHitchCommand\x12\x1d\n\x19HITCH_COMMAND_UNSPECIFIED\x10\x00\x12\n\n\x06RAISED\x10\x01\x12\x0b\n\x07LOWERED\x10\x02\x42\x07\n\x05state\"+\n\x08TaskList\x12\x1f\n\x05tasks\x18\x01 \x03(\x0b\x32\x10.carbon.rtc.Task\"H\n\x11ListTasksResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12\x1f\n\x05tasks\x18\x02 \x03(\x0b\x32\x10.carbon.rtc.Task\"l\n\x14SpatialPathTolerance\x12\x0f\n\x07heading\x18\x01 \x01(\x02\x12\x12\n\ncrosstrack\x18\x02 \x01(\x02\x12\x10\n\x08\x64istance\x18\x03 \x01(\x02\x12\x1d\n\x15\x63ontinuous_crosstrack\x18\x04 \x01(\x02\"\x9e\x03\n\x03Job\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12.\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nded_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12)\n\nobjectives\x18\x05 \x03(\x0b\x32\x15.carbon.rtc.Objective\x12 \n\x05state\x18\x06 \x01(\x0e\x32\x11.carbon.rtc.State\x12%\n\x04type\x18\x07 \x01(\x0e\x32\x17.carbon.rtc.Job.JobType\x12\x1a\n\rwork_order_id\x18\x08 \x01(\x04H\x00\x88\x01\x01\x12\x0f\n\x07\x66\x61rm_id\x18\t \x01(\t\x12\x10\n\x08\x66ield_id\x18\n \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x0b \x01(\t\x12\x10\n\x08priority\x18\x0c \x01(\x05\"3\n\x07JobType\x12\x18\n\x14JOB_TYPE_UNSPECIFIED\x10\x00\x12\x0e\n\nLASER_WEED\x10\x01\x42\x10\n\x0e_work_order_id\"(\n\x07JobList\x12\x1d\n\x04jobs\x18\x01 \x03(\x0b\x32\x0f.carbon.rtc.Job\"E\n\x10ListJobsResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12\x1d\n\x04jobs\x18\x02 \x03(\x0b\x32\x0f.carbon.rtc.Job\"\xb2\x01\n\tWorkOrder\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x30\n\x0cscheduled_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10\x64uration_minutes\x18\x04 \x01(\x05\x12\x1d\n\x04jobs\x18\x05 \x03(\x0b\x32\x0f.carbon.rtc.Job\x12 \n\x05state\x18\x06 \x01(\x0e\x32\x11.carbon.rtc.State\";\n\rWorkOrderList\x12*\n\x0bwork_orders\x18\x01 \x03(\x0b\x32\x15.carbon.rtc.WorkOrder\"X\n\x16ListWorkOrdersResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12*\n\x0bwork_orders\x18\x02 \x03(\x0b\x32\x15.carbon.rtc.WorkOrder\"\xea\x02\n\x0cIntervention\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07task_id\x18\x02 \x01(\x04\x12\x15\n\rqualification\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12 \n\x05state\x18\x05 \x01(\x0e\x32\x11.carbon.rtc.State\x12\x14\n\x0crobot_serial\x18\x06 \x01(\t\x12\x0e\n\x06job_id\x18\x07 \x01(\x04\x12\x39\n\x05\x63\x61use\x18\x08 \x01(\x0e\x32*.carbon.rtc.Intervention.InterventionCause\x12\x10\n\x08priority\x18\t \x01(\x05\"|\n\x11InterventionCause\x12\"\n\x1eINTERVENTION_CAUSE_UNSPECIFIED\x10\x00\x12\x14\n\x10SENSOR_TRIGGERED\x10\x01\x12\x18\n\x14SAFETY_DRIVER_ACTION\x10\x02\x12\x13\n\x0fTRACTOR_REQUEST\x10\x03\"B\n\x10InterventionList\x12.\n\x0cintervention\x18\x01 \x03(\x0b\x32\x18.carbon.rtc.Intervention\"A\n\x18ListInterventionsRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\"`\n\x19ListInterventionsResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12/\n\rinterventions\x18\x02 \x03(\x0b\x32\x18.carbon.rtc.Intervention\"K\n\x19\x43reateInterventionRequest\x12.\n\x0cintervention\x18\x01 \x01(\x0b\x32\x18.carbon.rtc.Intervention\"L\n\x1a\x43reateInterventionResponse\x12.\n\x0cintervention\x18\x01 \x01(\x0b\x32\x18.carbon.rtc.Intervention\"C\n\x14GetActiveTaskRequest\x12+\n\x10\x63urrent_location\x18\x02 \x01(\x0b\x32\x11.carbon.geo.Point\"7\n\x15GetActiveTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task\"!\n\x0eGetTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\"1\n\x0fGetTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task\"\xdd\x03\n\x11UpdateTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\x12%\n\x05state\x18\x02 \x01(\x0e\x32\x11.carbon.rtc.StateH\x00\x88\x01\x01\x12\x33\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x01\x88\x01\x01\x12.\n\x0estart_location\x18\x04 \x01(\x0b\x32\x11.carbon.geo.PointH\x02\x88\x01\x01\x12\x1a\n\rstart_heading\x18\x05 \x01(\x01H\x03\x88\x01\x01\x12\x31\n\x08\x65nded_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x04\x88\x01\x01\x12,\n\x0c\x65nd_location\x18\x07 \x01(\x0b\x32\x11.carbon.geo.PointH\x05\x88\x01\x01\x12\x18\n\x0b\x65nd_heading\x18\x08 \x01(\x01H\x06\x88\x01\x01\x12\x18\n\x0bstatus_info\x18\t \x01(\tH\x07\x88\x01\x01\x42\x08\n\x06_stateB\r\n\x0b_started_atB\x11\n\x0f_start_locationB\x10\n\x0e_start_headingB\x0b\n\t_ended_atB\x0f\n\r_end_locationB\x0e\n\x0c_end_headingB\x0e\n\x0c_status_info\"4\n\x12UpdateTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task*\x98\x01\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\t\n\x05READY\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\x12\r\n\tCOMPLETED\x10\x04\x12\r\n\tCANCELLED\x10\x05\x12\n\n\x06PAUSED\x10\x06\x12\n\n\x06\x46\x41ILED\x10\x07\x12\x10\n\x0c\x41\x43KNOWLEDGED\x10\x08\x12\x07\n\x03NEW\x10\t2\xc9\x03\n\nJobService\x12\x63\n\x12\x43reateIntervention\x12%.carbon.rtc.CreateInterventionRequest\x1a&.carbon.rtc.CreateInterventionResponse\x12T\n\rGetActiveTask\x12 .carbon.rtc.GetActiveTaskRequest\x1a!.carbon.rtc.GetActiveTaskResponse\x12\x42\n\x07GetTask\x12\x1a.carbon.rtc.GetTaskRequest\x1a\x1b.carbon.rtc.GetTaskResponse\x12o\n\x16GetNextActiveObjective\x12).carbon.rtc.GetNextActiveObjectiveRequest\x1a*.carbon.rtc.GetNextActiveObjectiveResponse\x12K\n\nUpdateTask\x12\x1d.carbon.rtc.UpdateTaskRequest\x1a\x1e.carbon.rtc.UpdateTaskResponseB\x0bZ\tproto/rtcb\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_protobuf_dot_duration__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,proto_dot_geo_dot_geo__pb2.DESCRIPTOR,])

_STATE = _descriptor.EnumDescriptor(
  name='State',
  full_name='carbon.rtc.State',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STATE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='READY', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IN_PROGRESS', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='COMPLETED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAUSED', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACKNOWLEDGED', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NEW', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5472,
  serialized_end=5624,
)
_sym_db.RegisterEnumDescriptor(_STATE)

State = enum_type_wrapper.EnumTypeWrapper(_STATE)
STATE_UNSPECIFIED = 0
PENDING = 1
READY = 2
IN_PROGRESS = 3
COMPLETED = 4
CANCELLED = 5
PAUSED = 6
FAILED = 7
ACKNOWLEDGED = 8
NEW = 9


_OBJECTIVE_OBJECTIVETYPE = _descriptor.EnumDescriptor(
  name='ObjectiveType',
  full_name='carbon.rtc.Objective.ObjectiveType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OBJECTIVE_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASER_WEED_ROW', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=376,
  serialized_end=443,
)
_sym_db.RegisterEnumDescriptor(_OBJECTIVE_OBJECTIVETYPE)

_TRACTORSTATE_GEAR = _descriptor.EnumDescriptor(
  name='Gear',
  full_name='carbon.rtc.TractorState.Gear',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GEAR_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PARK', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='REVERSE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NEUTRAL', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FORWARD', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='POWERZERO', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2606,
  serialized_end=2698,
)
_sym_db.RegisterEnumDescriptor(_TRACTORSTATE_GEAR)

_HITCHSTATE_HITCHCOMMAND = _descriptor.EnumDescriptor(
  name='HitchCommand',
  full_name='carbon.rtc.HitchState.HitchCommand',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HITCH_COMMAND_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RAISED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LOWERED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2800,
  serialized_end=2870,
)
_sym_db.RegisterEnumDescriptor(_HITCHSTATE_HITCHCOMMAND)

_JOB_JOBTYPE = _descriptor.EnumDescriptor(
  name='JobType',
  full_name='carbon.rtc.Job.JobType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='JOB_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASER_WEED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3456,
  serialized_end=3507,
)
_sym_db.RegisterEnumDescriptor(_JOB_JOBTYPE)

_INTERVENTION_INTERVENTIONCAUSE = _descriptor.EnumDescriptor(
  name='InterventionCause',
  full_name='carbon.rtc.Intervention.InterventionCause',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INTERVENTION_CAUSE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_TRIGGERED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SAFETY_DRIVER_ACTION', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TRACTOR_REQUEST', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4211,
  serialized_end=4335,
)
_sym_db.RegisterEnumDescriptor(_INTERVENTION_INTERVENTIONCAUSE)


_OBJECTIVE = _descriptor.Descriptor(
  name='Objective',
  full_name='carbon.rtc.Objective',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.Objective.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.rtc.Objective.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.rtc.Objective.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='progress_percent', full_name='carbon.rtc.Objective.progress_percent', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='priority', full_name='carbon.rtc.Objective.priority', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.rtc.Objective.type', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.rtc.Objective.data', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _OBJECTIVE_OBJECTIVETYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=182,
  serialized_end=443,
)


_OBJECTIVELIST = _descriptor.Descriptor(
  name='ObjectiveList',
  full_name='carbon.rtc.ObjectiveList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='objectives', full_name='carbon.rtc.ObjectiveList.objectives', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=445,
  serialized_end=503,
)


_LISTOBJECTIVESRESPONSE = _descriptor.Descriptor(
  name='ListObjectivesResponse',
  full_name='carbon.rtc.ListObjectivesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListObjectivesResponse.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='objectives', full_name='carbon.rtc.ListObjectivesResponse.objectives', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=505,
  serialized_end=592,
)


_GETNEXTACTIVEOBJECTIVEREQUEST = _descriptor.Descriptor(
  name='GetNextActiveObjectiveRequest',
  full_name='carbon.rtc.GetNextActiveObjectiveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='objective_id', full_name='carbon.rtc.GetNextActiveObjectiveRequest.objective_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=594,
  serialized_end=647,
)


_GETNEXTACTIVEOBJECTIVERESPONSE = _descriptor.Descriptor(
  name='GetNextActiveObjectiveResponse',
  full_name='carbon.rtc.GetNextActiveObjectiveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='objective', full_name='carbon.rtc.GetNextActiveObjectiveResponse.objective', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=649,
  serialized_end=723,
)


_TASK = _descriptor.Descriptor(
  name='Task',
  full_name='carbon.rtc.Task',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.Task.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.rtc.Task.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='started_at', full_name='carbon.rtc.Task.started_at', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ended_at', full_name='carbon.rtc.Task.ended_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_duration', full_name='carbon.rtc.Task.expected_duration', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_info', full_name='carbon.rtc.Task.status_info', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_tractor_state', full_name='carbon.rtc.Task.expected_tractor_state', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.Task.state', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='priority', full_name='carbon.rtc.Task.priority', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='objective_id', full_name='carbon.rtc.Task.objective_id', index=9,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_location', full_name='carbon.rtc.Task.start_location', index=10,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_heading', full_name='carbon.rtc.Task.start_heading', index=11,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_location', full_name='carbon.rtc.Task.end_location', index=12,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_heading', full_name='carbon.rtc.Task.end_heading', index=13,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='carbon.rtc.Task.sequence', index=14,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='manual', full_name='carbon.rtc.Task.manual', index=15,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_and_face', full_name='carbon.rtc.Task.go_to_and_face', index=16,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_path', full_name='carbon.rtc.Task.follow_path', index=17,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tractor_state', full_name='carbon.rtc.Task.tractor_state', index=18,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_weed', full_name='carbon.rtc.Task.laser_weed', index=19,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop_autonomy', full_name='carbon.rtc.Task.stop_autonomy', index=20,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_reversible_path', full_name='carbon.rtc.Task.go_to_reversible_path', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='task_details', full_name='carbon.rtc.Task.task_details',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=726,
  serialized_end=1628,
)


_STOPAUTONOMYTASK = _descriptor.Descriptor(
  name='StopAutonomyTask',
  full_name='carbon.rtc.StopAutonomyTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1630,
  serialized_end=1648,
)


_LASERWEEDTASK = _descriptor.Descriptor(
  name='LaserWeedTask',
  full_name='carbon.rtc.LaserWeedTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='carbon.rtc.LaserWeedTask.path', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='path_is_reversible', full_name='carbon.rtc.LaserWeedTask.path_is_reversible', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='carbon.rtc.LaserWeedTask.weeding_enabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_enabled', full_name='carbon.rtc.LaserWeedTask.thinning_enabled', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='manual', full_name='carbon.rtc.LaserWeedTask.manual', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tolerances', full_name='carbon.rtc.LaserWeedTask.tolerances', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1651,
  serialized_end=1853,
)


_SEQUENCETASK = _descriptor.Descriptor(
  name='SequenceTask',
  full_name='carbon.rtc.SequenceTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='carbon.rtc.SequenceTask.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='atomic', full_name='carbon.rtc.SequenceTask.atomic', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1855,
  serialized_end=1918,
)


_MANUALTASK = _descriptor.Descriptor(
  name='ManualTask',
  full_name='carbon.rtc.ManualTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='instructions', full_name='carbon.rtc.ManualTask.instructions', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1920,
  serialized_end=1954,
)


_GOTOANDFACETASK = _descriptor.Descriptor(
  name='GoToAndFaceTask',
  full_name='carbon.rtc.GoToAndFaceTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='point', full_name='carbon.rtc.GoToAndFaceTask.point', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='carbon.rtc.GoToAndFaceTask.heading', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1956,
  serialized_end=2024,
)


_GOTOREVERSIBLEPATHTASK = _descriptor.Descriptor(
  name='GoToReversiblePathTask',
  full_name='carbon.rtc.GoToReversiblePathTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='carbon.rtc.GoToReversiblePathTask.path', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tolerances', full_name='carbon.rtc.GoToReversiblePathTask.tolerances', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2026,
  serialized_end=2142,
)


_FOLLOWPATHTASK = _descriptor.Descriptor(
  name='FollowPathTask',
  full_name='carbon.rtc.FollowPathTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='carbon.rtc.FollowPathTask.path', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed', full_name='carbon.rtc.FollowPathTask.speed', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop_on_completion', full_name='carbon.rtc.FollowPathTask.stop_on_completion', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2144,
  serialized_end=2267,
)


_SPEEDSETTING = _descriptor.Descriptor(
  name='SpeedSetting',
  full_name='carbon.rtc.SpeedSetting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='constant_mph', full_name='carbon.rtc.SpeedSetting.constant_mph', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='remote_operator_controlled', full_name='carbon.rtc.SpeedSetting.remote_operator_controlled', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='implement_controlled', full_name='carbon.rtc.SpeedSetting.implement_controlled', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='speed', full_name='carbon.rtc.SpeedSetting.speed',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2270,
  serialized_end=2435,
)


_SETTRACTORSTATETASK = _descriptor.Descriptor(
  name='SetTractorStateTask',
  full_name='carbon.rtc.SetTractorStateTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.SetTractorStateTask.state', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2437,
  serialized_end=2499,
)


_TRACTORSTATE = _descriptor.Descriptor(
  name='TractorState',
  full_name='carbon.rtc.TractorState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='carbon.rtc.TractorState.gear', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch', full_name='carbon.rtc.TractorState.hitch', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRACTORSTATE_GEAR,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='state', full_name='carbon.rtc.TractorState.state',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2502,
  serialized_end=2707,
)


_HITCHSTATE = _descriptor.Descriptor(
  name='HitchState',
  full_name='carbon.rtc.HitchState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='command', full_name='carbon.rtc.HitchState.command', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='carbon.rtc.HitchState.position', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _HITCHSTATE_HITCHCOMMAND,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='state', full_name='carbon.rtc.HitchState.state',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2710,
  serialized_end=2879,
)


_TASKLIST = _descriptor.Descriptor(
  name='TaskList',
  full_name='carbon.rtc.TaskList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tasks', full_name='carbon.rtc.TaskList.tasks', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2881,
  serialized_end=2924,
)


_LISTTASKSRESPONSE = _descriptor.Descriptor(
  name='ListTasksResponse',
  full_name='carbon.rtc.ListTasksResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListTasksResponse.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tasks', full_name='carbon.rtc.ListTasksResponse.tasks', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2926,
  serialized_end=2998,
)


_SPATIALPATHTOLERANCE = _descriptor.Descriptor(
  name='SpatialPathTolerance',
  full_name='carbon.rtc.SpatialPathTolerance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='heading', full_name='carbon.rtc.SpatialPathTolerance.heading', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosstrack', full_name='carbon.rtc.SpatialPathTolerance.crosstrack', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='distance', full_name='carbon.rtc.SpatialPathTolerance.distance', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='continuous_crosstrack', full_name='carbon.rtc.SpatialPathTolerance.continuous_crosstrack', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3000,
  serialized_end=3108,
)


_JOB = _descriptor.Descriptor(
  name='Job',
  full_name='carbon.rtc.Job',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.Job.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.rtc.Job.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='started_at', full_name='carbon.rtc.Job.started_at', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ended_at', full_name='carbon.rtc.Job.ended_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='objectives', full_name='carbon.rtc.Job.objectives', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.Job.state', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.rtc.Job.type', index=6,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='work_order_id', full_name='carbon.rtc.Job.work_order_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='farm_id', full_name='carbon.rtc.Job.farm_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='field_id', full_name='carbon.rtc.Job.field_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_id', full_name='carbon.rtc.Job.customer_id', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='priority', full_name='carbon.rtc.Job.priority', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _JOB_JOBTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_work_order_id', full_name='carbon.rtc.Job._work_order_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3111,
  serialized_end=3525,
)


_JOBLIST = _descriptor.Descriptor(
  name='JobList',
  full_name='carbon.rtc.JobList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobs', full_name='carbon.rtc.JobList.jobs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3527,
  serialized_end=3567,
)


_LISTJOBSRESPONSE = _descriptor.Descriptor(
  name='ListJobsResponse',
  full_name='carbon.rtc.ListJobsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListJobsResponse.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='jobs', full_name='carbon.rtc.ListJobsResponse.jobs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3569,
  serialized_end=3638,
)


_WORKORDER = _descriptor.Descriptor(
  name='WorkOrder',
  full_name='carbon.rtc.WorkOrder',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.WorkOrder.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.rtc.WorkOrder.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scheduled_at', full_name='carbon.rtc.WorkOrder.scheduled_at', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_minutes', full_name='carbon.rtc.WorkOrder.duration_minutes', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='jobs', full_name='carbon.rtc.WorkOrder.jobs', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.WorkOrder.state', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3641,
  serialized_end=3819,
)


_WORKORDERLIST = _descriptor.Descriptor(
  name='WorkOrderList',
  full_name='carbon.rtc.WorkOrderList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='work_orders', full_name='carbon.rtc.WorkOrderList.work_orders', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3821,
  serialized_end=3880,
)


_LISTWORKORDERSRESPONSE = _descriptor.Descriptor(
  name='ListWorkOrdersResponse',
  full_name='carbon.rtc.ListWorkOrdersResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListWorkOrdersResponse.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='work_orders', full_name='carbon.rtc.ListWorkOrdersResponse.work_orders', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3882,
  serialized_end=3970,
)


_INTERVENTION = _descriptor.Descriptor(
  name='Intervention',
  full_name='carbon.rtc.Intervention',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.Intervention.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.rtc.Intervention.task_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='qualification', full_name='carbon.rtc.Intervention.qualification', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.rtc.Intervention.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.Intervention.state', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.rtc.Intervention.robot_serial', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='job_id', full_name='carbon.rtc.Intervention.job_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cause', full_name='carbon.rtc.Intervention.cause', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='priority', full_name='carbon.rtc.Intervention.priority', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _INTERVENTION_INTERVENTIONCAUSE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3973,
  serialized_end=4335,
)


_INTERVENTIONLIST = _descriptor.Descriptor(
  name='InterventionList',
  full_name='carbon.rtc.InterventionList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='intervention', full_name='carbon.rtc.InterventionList.intervention', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4337,
  serialized_end=4403,
)


_LISTINTERVENTIONSREQUEST = _descriptor.Descriptor(
  name='ListInterventionsRequest',
  full_name='carbon.rtc.ListInterventionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_size', full_name='carbon.rtc.ListInterventionsRequest.page_size', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListInterventionsRequest.page_token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4405,
  serialized_end=4470,
)


_LISTINTERVENTIONSRESPONSE = _descriptor.Descriptor(
  name='ListInterventionsResponse',
  full_name='carbon.rtc.ListInterventionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListInterventionsResponse.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interventions', full_name='carbon.rtc.ListInterventionsResponse.interventions', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4472,
  serialized_end=4568,
)


_CREATEINTERVENTIONREQUEST = _descriptor.Descriptor(
  name='CreateInterventionRequest',
  full_name='carbon.rtc.CreateInterventionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='intervention', full_name='carbon.rtc.CreateInterventionRequest.intervention', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4570,
  serialized_end=4645,
)


_CREATEINTERVENTIONRESPONSE = _descriptor.Descriptor(
  name='CreateInterventionResponse',
  full_name='carbon.rtc.CreateInterventionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='intervention', full_name='carbon.rtc.CreateInterventionResponse.intervention', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4647,
  serialized_end=4723,
)


_GETACTIVETASKREQUEST = _descriptor.Descriptor(
  name='GetActiveTaskRequest',
  full_name='carbon.rtc.GetActiveTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_location', full_name='carbon.rtc.GetActiveTaskRequest.current_location', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4725,
  serialized_end=4792,
)


_GETACTIVETASKRESPONSE = _descriptor.Descriptor(
  name='GetActiveTaskResponse',
  full_name='carbon.rtc.GetActiveTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task', full_name='carbon.rtc.GetActiveTaskResponse.task', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4794,
  serialized_end=4849,
)


_GETTASKREQUEST = _descriptor.Descriptor(
  name='GetTaskRequest',
  full_name='carbon.rtc.GetTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.rtc.GetTaskRequest.task_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4851,
  serialized_end=4884,
)


_GETTASKRESPONSE = _descriptor.Descriptor(
  name='GetTaskResponse',
  full_name='carbon.rtc.GetTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task', full_name='carbon.rtc.GetTaskResponse.task', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4886,
  serialized_end=4935,
)


_UPDATETASKREQUEST = _descriptor.Descriptor(
  name='UpdateTaskRequest',
  full_name='carbon.rtc.UpdateTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.rtc.UpdateTaskRequest.task_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.UpdateTaskRequest.state', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='started_at', full_name='carbon.rtc.UpdateTaskRequest.started_at', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_location', full_name='carbon.rtc.UpdateTaskRequest.start_location', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_heading', full_name='carbon.rtc.UpdateTaskRequest.start_heading', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ended_at', full_name='carbon.rtc.UpdateTaskRequest.ended_at', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_location', full_name='carbon.rtc.UpdateTaskRequest.end_location', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_heading', full_name='carbon.rtc.UpdateTaskRequest.end_heading', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_info', full_name='carbon.rtc.UpdateTaskRequest.status_info', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_state', full_name='carbon.rtc.UpdateTaskRequest._state',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_started_at', full_name='carbon.rtc.UpdateTaskRequest._started_at',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_start_location', full_name='carbon.rtc.UpdateTaskRequest._start_location',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_start_heading', full_name='carbon.rtc.UpdateTaskRequest._start_heading',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_ended_at', full_name='carbon.rtc.UpdateTaskRequest._ended_at',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_end_location', full_name='carbon.rtc.UpdateTaskRequest._end_location',
      index=5, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_end_heading', full_name='carbon.rtc.UpdateTaskRequest._end_heading',
      index=6, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_status_info', full_name='carbon.rtc.UpdateTaskRequest._status_info',
      index=7, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4938,
  serialized_end=5415,
)


_UPDATETASKRESPONSE = _descriptor.Descriptor(
  name='UpdateTaskResponse',
  full_name='carbon.rtc.UpdateTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task', full_name='carbon.rtc.UpdateTaskResponse.task', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5417,
  serialized_end=5469,
)

_OBJECTIVE.fields_by_name['type'].enum_type = _OBJECTIVE_OBJECTIVETYPE
_OBJECTIVE.fields_by_name['data'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_OBJECTIVE_OBJECTIVETYPE.containing_type = _OBJECTIVE
_OBJECTIVELIST.fields_by_name['objectives'].message_type = _OBJECTIVE
_LISTOBJECTIVESRESPONSE.fields_by_name['objectives'].message_type = _OBJECTIVE
_GETNEXTACTIVEOBJECTIVERESPONSE.fields_by_name['objective'].message_type = _OBJECTIVE
_TASK.fields_by_name['started_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASK.fields_by_name['ended_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASK.fields_by_name['expected_duration'].message_type = google_dot_protobuf_dot_duration__pb2._DURATION
_TASK.fields_by_name['expected_tractor_state'].message_type = _TRACTORSTATE
_TASK.fields_by_name['state'].enum_type = _STATE
_TASK.fields_by_name['start_location'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_TASK.fields_by_name['end_location'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_TASK.fields_by_name['sequence'].message_type = _SEQUENCETASK
_TASK.fields_by_name['manual'].message_type = _MANUALTASK
_TASK.fields_by_name['go_to_and_face'].message_type = _GOTOANDFACETASK
_TASK.fields_by_name['follow_path'].message_type = _FOLLOWPATHTASK
_TASK.fields_by_name['tractor_state'].message_type = _SETTRACTORSTATETASK
_TASK.fields_by_name['laser_weed'].message_type = _LASERWEEDTASK
_TASK.fields_by_name['stop_autonomy'].message_type = _STOPAUTONOMYTASK
_TASK.fields_by_name['go_to_reversible_path'].message_type = _GOTOREVERSIBLEPATHTASK
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['sequence'])
_TASK.fields_by_name['sequence'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['manual'])
_TASK.fields_by_name['manual'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['go_to_and_face'])
_TASK.fields_by_name['go_to_and_face'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['follow_path'])
_TASK.fields_by_name['follow_path'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['tractor_state'])
_TASK.fields_by_name['tractor_state'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['laser_weed'])
_TASK.fields_by_name['laser_weed'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['stop_autonomy'])
_TASK.fields_by_name['stop_autonomy'].containing_oneof = _TASK.oneofs_by_name['task_details']
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['go_to_reversible_path'])
_TASK.fields_by_name['go_to_reversible_path'].containing_oneof = _TASK.oneofs_by_name['task_details']
_LASERWEEDTASK.fields_by_name['path'].message_type = proto_dot_geo_dot_geo__pb2._LINESTRING
_LASERWEEDTASK.fields_by_name['tolerances'].message_type = _SPATIALPATHTOLERANCE
_SEQUENCETASK.fields_by_name['items'].message_type = _TASK
_GOTOANDFACETASK.fields_by_name['point'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_GOTOREVERSIBLEPATHTASK.fields_by_name['path'].message_type = proto_dot_geo_dot_geo__pb2._LINESTRING
_GOTOREVERSIBLEPATHTASK.fields_by_name['tolerances'].message_type = _SPATIALPATHTOLERANCE
_FOLLOWPATHTASK.fields_by_name['path'].message_type = proto_dot_geo_dot_geo__pb2._LINESTRING
_FOLLOWPATHTASK.fields_by_name['speed'].message_type = _SPEEDSETTING
_SPEEDSETTING.fields_by_name['remote_operator_controlled'].message_type = google_dot_protobuf_dot_empty__pb2._EMPTY
_SPEEDSETTING.fields_by_name['implement_controlled'].message_type = google_dot_protobuf_dot_empty__pb2._EMPTY
_SPEEDSETTING.oneofs_by_name['speed'].fields.append(
  _SPEEDSETTING.fields_by_name['constant_mph'])
_SPEEDSETTING.fields_by_name['constant_mph'].containing_oneof = _SPEEDSETTING.oneofs_by_name['speed']
_SPEEDSETTING.oneofs_by_name['speed'].fields.append(
  _SPEEDSETTING.fields_by_name['remote_operator_controlled'])
_SPEEDSETTING.fields_by_name['remote_operator_controlled'].containing_oneof = _SPEEDSETTING.oneofs_by_name['speed']
_SPEEDSETTING.oneofs_by_name['speed'].fields.append(
  _SPEEDSETTING.fields_by_name['implement_controlled'])
_SPEEDSETTING.fields_by_name['implement_controlled'].containing_oneof = _SPEEDSETTING.oneofs_by_name['speed']
_SETTRACTORSTATETASK.fields_by_name['state'].message_type = _TRACTORSTATE
_TRACTORSTATE.fields_by_name['gear'].enum_type = _TRACTORSTATE_GEAR
_TRACTORSTATE.fields_by_name['hitch'].message_type = _HITCHSTATE
_TRACTORSTATE_GEAR.containing_type = _TRACTORSTATE
_TRACTORSTATE.oneofs_by_name['state'].fields.append(
  _TRACTORSTATE.fields_by_name['gear'])
_TRACTORSTATE.fields_by_name['gear'].containing_oneof = _TRACTORSTATE.oneofs_by_name['state']
_TRACTORSTATE.oneofs_by_name['state'].fields.append(
  _TRACTORSTATE.fields_by_name['hitch'])
_TRACTORSTATE.fields_by_name['hitch'].containing_oneof = _TRACTORSTATE.oneofs_by_name['state']
_HITCHSTATE.fields_by_name['command'].enum_type = _HITCHSTATE_HITCHCOMMAND
_HITCHSTATE_HITCHCOMMAND.containing_type = _HITCHSTATE
_HITCHSTATE.oneofs_by_name['state'].fields.append(
  _HITCHSTATE.fields_by_name['command'])
_HITCHSTATE.fields_by_name['command'].containing_oneof = _HITCHSTATE.oneofs_by_name['state']
_HITCHSTATE.oneofs_by_name['state'].fields.append(
  _HITCHSTATE.fields_by_name['position'])
_HITCHSTATE.fields_by_name['position'].containing_oneof = _HITCHSTATE.oneofs_by_name['state']
_TASKLIST.fields_by_name['tasks'].message_type = _TASK
_LISTTASKSRESPONSE.fields_by_name['tasks'].message_type = _TASK
_JOB.fields_by_name['started_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_JOB.fields_by_name['ended_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_JOB.fields_by_name['objectives'].message_type = _OBJECTIVE
_JOB.fields_by_name['state'].enum_type = _STATE
_JOB.fields_by_name['type'].enum_type = _JOB_JOBTYPE
_JOB_JOBTYPE.containing_type = _JOB
_JOB.oneofs_by_name['_work_order_id'].fields.append(
  _JOB.fields_by_name['work_order_id'])
_JOB.fields_by_name['work_order_id'].containing_oneof = _JOB.oneofs_by_name['_work_order_id']
_JOBLIST.fields_by_name['jobs'].message_type = _JOB
_LISTJOBSRESPONSE.fields_by_name['jobs'].message_type = _JOB
_WORKORDER.fields_by_name['scheduled_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_WORKORDER.fields_by_name['jobs'].message_type = _JOB
_WORKORDER.fields_by_name['state'].enum_type = _STATE
_WORKORDERLIST.fields_by_name['work_orders'].message_type = _WORKORDER
_LISTWORKORDERSRESPONSE.fields_by_name['work_orders'].message_type = _WORKORDER
_INTERVENTION.fields_by_name['state'].enum_type = _STATE
_INTERVENTION.fields_by_name['cause'].enum_type = _INTERVENTION_INTERVENTIONCAUSE
_INTERVENTION_INTERVENTIONCAUSE.containing_type = _INTERVENTION
_INTERVENTIONLIST.fields_by_name['intervention'].message_type = _INTERVENTION
_LISTINTERVENTIONSRESPONSE.fields_by_name['interventions'].message_type = _INTERVENTION
_CREATEINTERVENTIONREQUEST.fields_by_name['intervention'].message_type = _INTERVENTION
_CREATEINTERVENTIONRESPONSE.fields_by_name['intervention'].message_type = _INTERVENTION
_GETACTIVETASKREQUEST.fields_by_name['current_location'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_GETACTIVETASKRESPONSE.fields_by_name['task'].message_type = _TASK
_GETTASKRESPONSE.fields_by_name['task'].message_type = _TASK
_UPDATETASKREQUEST.fields_by_name['state'].enum_type = _STATE
_UPDATETASKREQUEST.fields_by_name['started_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATETASKREQUEST.fields_by_name['start_location'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_UPDATETASKREQUEST.fields_by_name['ended_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATETASKREQUEST.fields_by_name['end_location'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_UPDATETASKREQUEST.oneofs_by_name['_state'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['state'])
_UPDATETASKREQUEST.fields_by_name['state'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_state']
_UPDATETASKREQUEST.oneofs_by_name['_started_at'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['started_at'])
_UPDATETASKREQUEST.fields_by_name['started_at'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_started_at']
_UPDATETASKREQUEST.oneofs_by_name['_start_location'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['start_location'])
_UPDATETASKREQUEST.fields_by_name['start_location'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_start_location']
_UPDATETASKREQUEST.oneofs_by_name['_start_heading'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['start_heading'])
_UPDATETASKREQUEST.fields_by_name['start_heading'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_start_heading']
_UPDATETASKREQUEST.oneofs_by_name['_ended_at'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['ended_at'])
_UPDATETASKREQUEST.fields_by_name['ended_at'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_ended_at']
_UPDATETASKREQUEST.oneofs_by_name['_end_location'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['end_location'])
_UPDATETASKREQUEST.fields_by_name['end_location'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_end_location']
_UPDATETASKREQUEST.oneofs_by_name['_end_heading'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['end_heading'])
_UPDATETASKREQUEST.fields_by_name['end_heading'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_end_heading']
_UPDATETASKREQUEST.oneofs_by_name['_status_info'].fields.append(
  _UPDATETASKREQUEST.fields_by_name['status_info'])
_UPDATETASKREQUEST.fields_by_name['status_info'].containing_oneof = _UPDATETASKREQUEST.oneofs_by_name['_status_info']
_UPDATETASKRESPONSE.fields_by_name['task'].message_type = _TASK
DESCRIPTOR.message_types_by_name['Objective'] = _OBJECTIVE
DESCRIPTOR.message_types_by_name['ObjectiveList'] = _OBJECTIVELIST
DESCRIPTOR.message_types_by_name['ListObjectivesResponse'] = _LISTOBJECTIVESRESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveObjectiveRequest'] = _GETNEXTACTIVEOBJECTIVEREQUEST
DESCRIPTOR.message_types_by_name['GetNextActiveObjectiveResponse'] = _GETNEXTACTIVEOBJECTIVERESPONSE
DESCRIPTOR.message_types_by_name['Task'] = _TASK
DESCRIPTOR.message_types_by_name['StopAutonomyTask'] = _STOPAUTONOMYTASK
DESCRIPTOR.message_types_by_name['LaserWeedTask'] = _LASERWEEDTASK
DESCRIPTOR.message_types_by_name['SequenceTask'] = _SEQUENCETASK
DESCRIPTOR.message_types_by_name['ManualTask'] = _MANUALTASK
DESCRIPTOR.message_types_by_name['GoToAndFaceTask'] = _GOTOANDFACETASK
DESCRIPTOR.message_types_by_name['GoToReversiblePathTask'] = _GOTOREVERSIBLEPATHTASK
DESCRIPTOR.message_types_by_name['FollowPathTask'] = _FOLLOWPATHTASK
DESCRIPTOR.message_types_by_name['SpeedSetting'] = _SPEEDSETTING
DESCRIPTOR.message_types_by_name['SetTractorStateTask'] = _SETTRACTORSTATETASK
DESCRIPTOR.message_types_by_name['TractorState'] = _TRACTORSTATE
DESCRIPTOR.message_types_by_name['HitchState'] = _HITCHSTATE
DESCRIPTOR.message_types_by_name['TaskList'] = _TASKLIST
DESCRIPTOR.message_types_by_name['ListTasksResponse'] = _LISTTASKSRESPONSE
DESCRIPTOR.message_types_by_name['SpatialPathTolerance'] = _SPATIALPATHTOLERANCE
DESCRIPTOR.message_types_by_name['Job'] = _JOB
DESCRIPTOR.message_types_by_name['JobList'] = _JOBLIST
DESCRIPTOR.message_types_by_name['ListJobsResponse'] = _LISTJOBSRESPONSE
DESCRIPTOR.message_types_by_name['WorkOrder'] = _WORKORDER
DESCRIPTOR.message_types_by_name['WorkOrderList'] = _WORKORDERLIST
DESCRIPTOR.message_types_by_name['ListWorkOrdersResponse'] = _LISTWORKORDERSRESPONSE
DESCRIPTOR.message_types_by_name['Intervention'] = _INTERVENTION
DESCRIPTOR.message_types_by_name['InterventionList'] = _INTERVENTIONLIST
DESCRIPTOR.message_types_by_name['ListInterventionsRequest'] = _LISTINTERVENTIONSREQUEST
DESCRIPTOR.message_types_by_name['ListInterventionsResponse'] = _LISTINTERVENTIONSRESPONSE
DESCRIPTOR.message_types_by_name['CreateInterventionRequest'] = _CREATEINTERVENTIONREQUEST
DESCRIPTOR.message_types_by_name['CreateInterventionResponse'] = _CREATEINTERVENTIONRESPONSE
DESCRIPTOR.message_types_by_name['GetActiveTaskRequest'] = _GETACTIVETASKREQUEST
DESCRIPTOR.message_types_by_name['GetActiveTaskResponse'] = _GETACTIVETASKRESPONSE
DESCRIPTOR.message_types_by_name['GetTaskRequest'] = _GETTASKREQUEST
DESCRIPTOR.message_types_by_name['GetTaskResponse'] = _GETTASKRESPONSE
DESCRIPTOR.message_types_by_name['UpdateTaskRequest'] = _UPDATETASKREQUEST
DESCRIPTOR.message_types_by_name['UpdateTaskResponse'] = _UPDATETASKRESPONSE
DESCRIPTOR.enum_types_by_name['State'] = _STATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Objective = _reflection.GeneratedProtocolMessageType('Objective', (_message.Message,), {
  'DESCRIPTOR' : _OBJECTIVE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.Objective)
  })
_sym_db.RegisterMessage(Objective)

ObjectiveList = _reflection.GeneratedProtocolMessageType('ObjectiveList', (_message.Message,), {
  'DESCRIPTOR' : _OBJECTIVELIST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ObjectiveList)
  })
_sym_db.RegisterMessage(ObjectiveList)

ListObjectivesResponse = _reflection.GeneratedProtocolMessageType('ListObjectivesResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTOBJECTIVESRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListObjectivesResponse)
  })
_sym_db.RegisterMessage(ListObjectivesResponse)

GetNextActiveObjectiveRequest = _reflection.GeneratedProtocolMessageType('GetNextActiveObjectiveRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEOBJECTIVEREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetNextActiveObjectiveRequest)
  })
_sym_db.RegisterMessage(GetNextActiveObjectiveRequest)

GetNextActiveObjectiveResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveObjectiveResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEOBJECTIVERESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetNextActiveObjectiveResponse)
  })
_sym_db.RegisterMessage(GetNextActiveObjectiveResponse)

Task = _reflection.GeneratedProtocolMessageType('Task', (_message.Message,), {
  'DESCRIPTOR' : _TASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.Task)
  })
_sym_db.RegisterMessage(Task)

StopAutonomyTask = _reflection.GeneratedProtocolMessageType('StopAutonomyTask', (_message.Message,), {
  'DESCRIPTOR' : _STOPAUTONOMYTASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.StopAutonomyTask)
  })
_sym_db.RegisterMessage(StopAutonomyTask)

LaserWeedTask = _reflection.GeneratedProtocolMessageType('LaserWeedTask', (_message.Message,), {
  'DESCRIPTOR' : _LASERWEEDTASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.LaserWeedTask)
  })
_sym_db.RegisterMessage(LaserWeedTask)

SequenceTask = _reflection.GeneratedProtocolMessageType('SequenceTask', (_message.Message,), {
  'DESCRIPTOR' : _SEQUENCETASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SequenceTask)
  })
_sym_db.RegisterMessage(SequenceTask)

ManualTask = _reflection.GeneratedProtocolMessageType('ManualTask', (_message.Message,), {
  'DESCRIPTOR' : _MANUALTASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ManualTask)
  })
_sym_db.RegisterMessage(ManualTask)

GoToAndFaceTask = _reflection.GeneratedProtocolMessageType('GoToAndFaceTask', (_message.Message,), {
  'DESCRIPTOR' : _GOTOANDFACETASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GoToAndFaceTask)
  })
_sym_db.RegisterMessage(GoToAndFaceTask)

GoToReversiblePathTask = _reflection.GeneratedProtocolMessageType('GoToReversiblePathTask', (_message.Message,), {
  'DESCRIPTOR' : _GOTOREVERSIBLEPATHTASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GoToReversiblePathTask)
  })
_sym_db.RegisterMessage(GoToReversiblePathTask)

FollowPathTask = _reflection.GeneratedProtocolMessageType('FollowPathTask', (_message.Message,), {
  'DESCRIPTOR' : _FOLLOWPATHTASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.FollowPathTask)
  })
_sym_db.RegisterMessage(FollowPathTask)

SpeedSetting = _reflection.GeneratedProtocolMessageType('SpeedSetting', (_message.Message,), {
  'DESCRIPTOR' : _SPEEDSETTING,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SpeedSetting)
  })
_sym_db.RegisterMessage(SpeedSetting)

SetTractorStateTask = _reflection.GeneratedProtocolMessageType('SetTractorStateTask', (_message.Message,), {
  'DESCRIPTOR' : _SETTRACTORSTATETASK,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SetTractorStateTask)
  })
_sym_db.RegisterMessage(SetTractorStateTask)

TractorState = _reflection.GeneratedProtocolMessageType('TractorState', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORSTATE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.TractorState)
  })
_sym_db.RegisterMessage(TractorState)

HitchState = _reflection.GeneratedProtocolMessageType('HitchState', (_message.Message,), {
  'DESCRIPTOR' : _HITCHSTATE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.HitchState)
  })
_sym_db.RegisterMessage(HitchState)

TaskList = _reflection.GeneratedProtocolMessageType('TaskList', (_message.Message,), {
  'DESCRIPTOR' : _TASKLIST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.TaskList)
  })
_sym_db.RegisterMessage(TaskList)

ListTasksResponse = _reflection.GeneratedProtocolMessageType('ListTasksResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTTASKSRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListTasksResponse)
  })
_sym_db.RegisterMessage(ListTasksResponse)

SpatialPathTolerance = _reflection.GeneratedProtocolMessageType('SpatialPathTolerance', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALPATHTOLERANCE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SpatialPathTolerance)
  })
_sym_db.RegisterMessage(SpatialPathTolerance)

Job = _reflection.GeneratedProtocolMessageType('Job', (_message.Message,), {
  'DESCRIPTOR' : _JOB,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.Job)
  })
_sym_db.RegisterMessage(Job)

JobList = _reflection.GeneratedProtocolMessageType('JobList', (_message.Message,), {
  'DESCRIPTOR' : _JOBLIST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.JobList)
  })
_sym_db.RegisterMessage(JobList)

ListJobsResponse = _reflection.GeneratedProtocolMessageType('ListJobsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTJOBSRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListJobsResponse)
  })
_sym_db.RegisterMessage(ListJobsResponse)

WorkOrder = _reflection.GeneratedProtocolMessageType('WorkOrder', (_message.Message,), {
  'DESCRIPTOR' : _WORKORDER,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.WorkOrder)
  })
_sym_db.RegisterMessage(WorkOrder)

WorkOrderList = _reflection.GeneratedProtocolMessageType('WorkOrderList', (_message.Message,), {
  'DESCRIPTOR' : _WORKORDERLIST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.WorkOrderList)
  })
_sym_db.RegisterMessage(WorkOrderList)

ListWorkOrdersResponse = _reflection.GeneratedProtocolMessageType('ListWorkOrdersResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTWORKORDERSRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListWorkOrdersResponse)
  })
_sym_db.RegisterMessage(ListWorkOrdersResponse)

Intervention = _reflection.GeneratedProtocolMessageType('Intervention', (_message.Message,), {
  'DESCRIPTOR' : _INTERVENTION,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.Intervention)
  })
_sym_db.RegisterMessage(Intervention)

InterventionList = _reflection.GeneratedProtocolMessageType('InterventionList', (_message.Message,), {
  'DESCRIPTOR' : _INTERVENTIONLIST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.InterventionList)
  })
_sym_db.RegisterMessage(InterventionList)

ListInterventionsRequest = _reflection.GeneratedProtocolMessageType('ListInterventionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTINTERVENTIONSREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListInterventionsRequest)
  })
_sym_db.RegisterMessage(ListInterventionsRequest)

ListInterventionsResponse = _reflection.GeneratedProtocolMessageType('ListInterventionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTINTERVENTIONSRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListInterventionsResponse)
  })
_sym_db.RegisterMessage(ListInterventionsResponse)

CreateInterventionRequest = _reflection.GeneratedProtocolMessageType('CreateInterventionRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEINTERVENTIONREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.CreateInterventionRequest)
  })
_sym_db.RegisterMessage(CreateInterventionRequest)

CreateInterventionResponse = _reflection.GeneratedProtocolMessageType('CreateInterventionResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEINTERVENTIONRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.CreateInterventionResponse)
  })
_sym_db.RegisterMessage(CreateInterventionResponse)

GetActiveTaskRequest = _reflection.GeneratedProtocolMessageType('GetActiveTaskRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETACTIVETASKREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetActiveTaskRequest)
  })
_sym_db.RegisterMessage(GetActiveTaskRequest)

GetActiveTaskResponse = _reflection.GeneratedProtocolMessageType('GetActiveTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETACTIVETASKRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetActiveTaskResponse)
  })
_sym_db.RegisterMessage(GetActiveTaskResponse)

GetTaskRequest = _reflection.GeneratedProtocolMessageType('GetTaskRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTASKREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetTaskRequest)
  })
_sym_db.RegisterMessage(GetTaskRequest)

GetTaskResponse = _reflection.GeneratedProtocolMessageType('GetTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTASKRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetTaskResponse)
  })
_sym_db.RegisterMessage(GetTaskResponse)

UpdateTaskRequest = _reflection.GeneratedProtocolMessageType('UpdateTaskRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATETASKREQUEST,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.UpdateTaskRequest)
  })
_sym_db.RegisterMessage(UpdateTaskRequest)

UpdateTaskResponse = _reflection.GeneratedProtocolMessageType('UpdateTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATETASKRESPONSE,
  '__module__' : 'proto.rtc.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.UpdateTaskResponse)
  })
_sym_db.RegisterMessage(UpdateTaskResponse)


DESCRIPTOR._options = None

_JOBSERVICE = _descriptor.ServiceDescriptor(
  name='JobService',
  full_name='carbon.rtc.JobService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=5627,
  serialized_end=6084,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateIntervention',
    full_name='carbon.rtc.JobService.CreateIntervention',
    index=0,
    containing_service=None,
    input_type=_CREATEINTERVENTIONREQUEST,
    output_type=_CREATEINTERVENTIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetActiveTask',
    full_name='carbon.rtc.JobService.GetActiveTask',
    index=1,
    containing_service=None,
    input_type=_GETACTIVETASKREQUEST,
    output_type=_GETACTIVETASKRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTask',
    full_name='carbon.rtc.JobService.GetTask',
    index=2,
    containing_service=None,
    input_type=_GETTASKREQUEST,
    output_type=_GETTASKRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveObjective',
    full_name='carbon.rtc.JobService.GetNextActiveObjective',
    index=3,
    containing_service=None,
    input_type=_GETNEXTACTIVEOBJECTIVEREQUEST,
    output_type=_GETNEXTACTIVEOBJECTIVERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateTask',
    full_name='carbon.rtc.JobService.UpdateTask',
    index=4,
    containing_service=None,
    input_type=_UPDATETASKREQUEST,
    output_type=_UPDATETASKRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_JOBSERVICE)

DESCRIPTOR.services_by_name['JobService'] = _JOBSERVICE

# @@protoc_insertion_point(module_scope)
