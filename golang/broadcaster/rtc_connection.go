package main

import (
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"

	"github.com/pion/webrtc/v4"
	"github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/rtc"
)

type ConnectionDetails struct {
	HostID   string
	PeerID   string
	StreamID string
}
type MediaProvider interface {
	AddTrack() (int, webrtc.TrackLocal, error)
	RemoveTrack(int)
}
type RTCPeerConnection struct {
	signalClient                 SignalingClient
	peerConnection               *webrtc.PeerConnection
	channelMu                    sync.Mutex
	dataChannels                 map[string]*webrtc.DataChannel
	verbose                      bool
	pendingIceCandidates         []*webrtc.ICECandidate
	pendingReceivedIceCandidates []webrtc.ICECandidateInit
	hostID                       string
	peerID                       string
	connectionID                 string
	closed                       atomic.Bool
	mediaProvider                MediaProvider
	trackID                      int
	dataBus                      DataBusIF
	trustedForwarder             bool
	mediaSupported               bool
	userIdLookup                 UserIdLookup
}

func (r *RTCPeerConnection) String() string {
	details := fmt.Sprintf("PeerID: %v, trustedForwarder: %v", r.peerID, r.trustedForwarder)
	userId, found := r.userIdLookup(r.peerID)
	if found {
		details += fmt.Sprintf(", userID: %v", userId)
	}
	return details
}

type connCfg struct {
	verbose          bool
	turnUrls         []string
	trustedForwarder bool
}

type ConnOptions func(*connCfg)
type UserIdLookup func(string) (string, bool)

func WithVerboseLogging(verbose bool) ConnOptions {
	return func(cfg *connCfg) {
		cfg.verbose = verbose
	}
}
func WithTurnUrls(turnUrls []string) ConnOptions {
	return func(cfg *connCfg) {
		cfg.turnUrls = turnUrls
	}
}
func WithTrustedForwarder(trustedForwarder bool) ConnOptions {
	return func(cfg *connCfg) {
		cfg.trustedForwarder = trustedForwarder
	}
}

func (r *RTCPeerConnection) ID() string {
	return fmt.Sprintf("%v:%v", r.connectionID, r.peerID)
}
func (r *RTCPeerConnection) CloseConn() {
	if r.closed.Load() {
		return
	}
	r.channelMu.Lock()
	defer r.channelMu.Unlock()
	for label, channel := range r.dataChannels {
		channel.Close()
		delete(r.dataChannels, label)
	}
	if r.peerConnection != nil {
		r.peerConnection.Close()
	}
	if r.mediaSupported {
		r.mediaProvider.RemoveTrack(r.trackID)
	}
	r.closed.Store(true)
}

func (r *RTCPeerConnection) IsClosed() bool {
	return r.closed.Load()
}
func (r *RTCPeerConnection) onDataChannel(channel *webrtc.DataChannel) {
	channel.OnOpen(func() {
		r.channelMu.Lock()
		defer r.channelMu.Unlock()
		r.dataChannels[channel.Label()] = channel
		if r.verbose {
			logrus.Infof("data channel '%v' opened", channel.Label())
		}
		r.dataBus.OnConnect(r.peerID, channel)
	})
	channel.OnMessage(func(msg webrtc.DataChannelMessage) {
		r.dataBus.OnMessage(r.peerID, channel, r.trustedForwarder, msg.Data, true)
	})
	channel.OnClose(func() {
		r.channelMu.Lock()
		defer r.channelMu.Unlock()
		if r.verbose {
			logrus.Infof("rtc data channel '%v' closed", channel.Label())
		}
		r.dataBus.OnDisconnect(r.peerID, channel.Label())
		delete(r.dataChannels, channel.Label())
	})
}

func NewRTCPeerConnection(api *webrtc.API, signalClient SignalingClient, connDetails *ConnectionDetails, mediaProvider MediaProvider, dataBus DataBusIF, mediaSupported bool, userIdLookup UserIdLookup, ops ...ConnOptions) (*RTCPeerConnection, error) {
	cfg := &connCfg{
		turnUrls: make([]string, 0),
	}
	for _, f := range ops {
		f(cfg)
	}
	r := &RTCPeerConnection{
		signalClient:                 signalClient,
		dataChannels:                 make(map[string]*webrtc.DataChannel),
		verbose:                      cfg.verbose,
		pendingIceCandidates:         make([]*webrtc.ICECandidate, 0),
		pendingReceivedIceCandidates: make([]webrtc.ICECandidateInit, 0),
		hostID:                       connDetails.HostID,
		peerID:                       connDetails.PeerID,
		connectionID:                 connDetails.StreamID,
		mediaProvider:                mediaProvider,
		dataBus:                      dataBus,
		trustedForwarder:             cfg.trustedForwarder,
		mediaSupported:               mediaSupported,
		userIdLookup:                 userIdLookup,
	}
	config := webrtc.Configuration{
		ICEServers: []webrtc.ICEServer{{URLs: cfg.turnUrls}},
	}
	peerConnection, err := api.NewPeerConnection(config)
	if err != nil {
		return nil, err
	}
	if r.mediaSupported {
		trackID, track, err := mediaProvider.AddTrack()
		if err != nil {
			return nil, err
		}
		r.trackID = trackID
		rtpSender, err := peerConnection.AddTrack(track)

		if err != nil {
			return nil, err
		}

		// Read incoming RTCP packets
		// Before these packets are returned they are processed by interceptors. For things
		// like NACK this needs to be called.
		go func() {
			rtcpBuf := make([]byte, 1500)
			for {
				if _, _, rtcpErr := rtpSender.Read(rtcpBuf); rtcpErr != nil {
					return
				}
			}
		}()
	}
	peerConnection.OnICEConnectionStateChange(func(connectionState webrtc.ICEConnectionState) {
		if r.verbose {
			logrus.Infof("ICE Connection State has changed to %v", connectionState.String())
		}
	})
	peerConnection.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		if r.verbose {
			logrus.Infof("Connection State has changed to %v", state.String())
		}
		switch state {
		case webrtc.PeerConnectionStateDisconnected, webrtc.PeerConnectionStateFailed, webrtc.PeerConnectionStateClosed:
			r.CloseConn()
		default:
		}
	})

	peerConnection.OnICECandidate(func(candidate *webrtc.ICECandidate) {
		if candidate == nil {
			return
		}
		if peerConnection.RemoteDescription() == nil {
			if r.verbose {
				fmt.Println("remote desc is nil, adding pending candidate for data", candidate.ToJSON())
			}
			r.AddPendingIceCandidate(candidate)
			return
		}
		r.sendIceCandidate(candidate)

	})
	r.peerConnection = peerConnection
	ordered := true
	negotiated := true
	maxRetransmits := uint16(0)
	for label, mapId := range rtc.DataChannels() {
		tmpId := mapId // mapId is reused in loop so cannot directly deref need a temp for go
		labeledChan, err := peerConnection.CreateDataChannel(label, &webrtc.DataChannelInit{
			Ordered:        &ordered,
			MaxRetransmits: &maxRetransmits,
			Negotiated:     &negotiated,
			ID:             &tmpId,
		})
		if err != nil {
			return nil, err
		}
		r.onDataChannel(labeledChan)
	}

	desc, err := peerConnection.CreateOffer(nil)
	if err != nil {
		return nil, err
	}
	if err := peerConnection.SetLocalDescription(desc); err != nil {
		return nil, err
	}
	sdpMsg, err := rtc.NewMessageWithContent(rtc.SDP, rtc.Sdp{
		TargetHostID: r.peerID,
		HostID:       r.hostID,
		ConnectionID: r.connectionID,
		SDP:          desc,
	})
	if err != nil {
		logrus.Error("Failed to marshal SDP offer")
		return nil, err
	}
	if r.verbose {
		logrus.Infof("sdp offer desc: %v", desc)
	}
	r.signalClient.Send(sdpMsg)
	return r, nil
}
func (r *RTCPeerConnection) HasEnded() bool {
	return r.closed.Load() || r.peerConnection.ConnectionState() == webrtc.PeerConnectionStateDisconnected || r.peerConnection.ConnectionState() == webrtc.PeerConnectionStateFailed || r.peerConnection.ConnectionState() == webrtc.PeerConnectionStateClosed
}
func (r *RTCPeerConnection) IsConnected() bool {
	return !r.closed.Load() && r.peerConnection.ConnectionState() == webrtc.PeerConnectionStateConnected
}

func (r *RTCPeerConnection) SetRemoteDescription(sdp webrtc.SessionDescription) error {
	if r.verbose {
		logrus.Infof("setting remote desc sdp=%v", sdp)
	}
	if err := r.peerConnection.SetRemoteDescription(sdp); err != nil {
		return err
	}
	r.channelMu.Lock()
	defer r.channelMu.Unlock()
	for _, candidate := range r.pendingReceivedIceCandidates {
		if r.verbose {
			logrus.Infof("Adding cached candidate: %v", candidate)
		}
		r.peerConnection.AddICECandidate(candidate)
	}
	for _, candidate := range r.pendingIceCandidates {
		r.sendIceCandidate(candidate)
	}
	r.pendingIceCandidates = make([]*webrtc.ICECandidate, 0)
	r.pendingReceivedIceCandidates = make([]webrtc.ICECandidateInit, 0)
	return nil
}

func (r *RTCPeerConnection) sendIceCandidate(candidate *webrtc.ICECandidate) {
	tmp := candidate.ToJSON()
	if r.verbose {
		logrus.Infof("sending ice candidate '%v'", tmp)
	}
	candidateResp, err := rtc.NewMessageWithContent(rtc.CANDIDATE, rtc.Candidate{
		TargetHostID: r.peerID,
		HostID:       r.hostID,
		ConnectionID: r.connectionID,
		Candidate:    &tmp,
	})
	if err != nil {
		logrus.Errorf("ice candidate json marshal failure err: %v", err)
		return
	}
	r.signalClient.Send(candidateResp)
}

func (r *RTCPeerConnection) AddPendingIceCandidate(candidate *webrtc.ICECandidate) {
	r.channelMu.Lock()
	defer r.channelMu.Unlock()
	r.pendingIceCandidates = append(r.pendingIceCandidates, candidate)
}
func (r *RTCPeerConnection) AddICECandidate(candidate webrtc.ICECandidateInit) error {
	r.channelMu.Lock()
	defer r.channelMu.Unlock()
	if r.peerConnection == nil {
		return fmt.Errorf("cannot add ice candidates with no peer connection")
	}
	if r.peerConnection.RemoteDescription() == nil {
		if r.verbose {
			logrus.Infof("data channel: received candidate '%v' from remote before sdp caching.", candidate)
		}
		r.pendingReceivedIceCandidates = append(r.pendingReceivedIceCandidates, candidate)
		return nil
	}
	if r.verbose {
		logrus.Infof("data channel: received candidate '%v' from remote.", candidate)
	}
	return r.peerConnection.AddICECandidate(candidate)
}

func (r *RTCPeerConnection) SendMsgOnChannel(channelLabel string, msg json.RawMessage) error {
	r.channelMu.Lock()
	defer r.channelMu.Unlock()
	if !r.IsConnected() {
		return fmt.Errorf("Webrtc is not connected %v for connection %v", r.peerID, r.connectionID)
	}
	if channel, ok := r.dataChannels[channelLabel]; ok {
		return channel.Send(msg)
	}
	return fmt.Errorf("Channel %v not found", channelLabel)
}
