// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: proto/rtc/jobs.proto

package rtc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	geo "github.com/carbonrobotics/robot/golang/generated/proto/geo"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Unified State for all objects
type State int32

const (
	State_STATE_UNSPECIFIED State = 0
	State_PENDING           State = 1
	State_READY             State = 2
	State_IN_PROGRESS       State = 3
	State_COMPLETED         State = 4
	State_CANCELLED         State = 5
	State_PAUSED            State = 6
	State_FAILED            State = 7
	State_ACKNOWLEDGED      State = 8
	State_NEW               State = 9
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "PENDING",
		2: "READY",
		3: "IN_PROGRESS",
		4: "COMPLETED",
		5: "CANCELLED",
		6: "PAUSED",
		7: "FAILED",
		8: "ACKNOWLEDGED",
		9: "NEW",
	}
	State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"PENDING":           1,
		"READY":             2,
		"IN_PROGRESS":       3,
		"COMPLETED":         4,
		"CANCELLED":         5,
		"PAUSED":            6,
		"FAILED":            7,
		"ACKNOWLEDGED":      8,
		"NEW":               9,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[0].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[0]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{0}
}

type Objective_ObjectiveType int32

const (
	Objective_OBJECTIVE_TYPE_UNSPECIFIED Objective_ObjectiveType = 0
	Objective_LASER_WEED_ROW             Objective_ObjectiveType = 1
)

// Enum value maps for Objective_ObjectiveType.
var (
	Objective_ObjectiveType_name = map[int32]string{
		0: "OBJECTIVE_TYPE_UNSPECIFIED",
		1: "LASER_WEED_ROW",
	}
	Objective_ObjectiveType_value = map[string]int32{
		"OBJECTIVE_TYPE_UNSPECIFIED": 0,
		"LASER_WEED_ROW":             1,
	}
)

func (x Objective_ObjectiveType) Enum() *Objective_ObjectiveType {
	p := new(Objective_ObjectiveType)
	*p = x
	return p
}

func (x Objective_ObjectiveType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Objective_ObjectiveType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[1].Descriptor()
}

func (Objective_ObjectiveType) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[1]
}

func (x Objective_ObjectiveType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Objective_ObjectiveType.Descriptor instead.
func (Objective_ObjectiveType) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{0, 0}
}

type TractorState_Gear int32

const (
	TractorState_GEAR_UNSPECIFIED TractorState_Gear = 0
	TractorState_PARK             TractorState_Gear = 1
	TractorState_REVERSE          TractorState_Gear = 2
	TractorState_NEUTRAL          TractorState_Gear = 3
	TractorState_FORWARD          TractorState_Gear = 4
	TractorState_POWERZERO        TractorState_Gear = 5
)

// Enum value maps for TractorState_Gear.
var (
	TractorState_Gear_name = map[int32]string{
		0: "GEAR_UNSPECIFIED",
		1: "PARK",
		2: "REVERSE",
		3: "NEUTRAL",
		4: "FORWARD",
		5: "POWERZERO",
	}
	TractorState_Gear_value = map[string]int32{
		"GEAR_UNSPECIFIED": 0,
		"PARK":             1,
		"REVERSE":          2,
		"NEUTRAL":          3,
		"FORWARD":          4,
		"POWERZERO":        5,
	}
)

func (x TractorState_Gear) Enum() *TractorState_Gear {
	p := new(TractorState_Gear)
	*p = x
	return p
}

func (x TractorState_Gear) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TractorState_Gear) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[2].Descriptor()
}

func (TractorState_Gear) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[2]
}

func (x TractorState_Gear) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TractorState_Gear.Descriptor instead.
func (TractorState_Gear) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{15, 0}
}

type HitchState_HitchCommand int32

const (
	HitchState_HITCH_COMMAND_UNSPECIFIED HitchState_HitchCommand = 0
	HitchState_RAISED                    HitchState_HitchCommand = 1
	HitchState_LOWERED                   HitchState_HitchCommand = 2
)

// Enum value maps for HitchState_HitchCommand.
var (
	HitchState_HitchCommand_name = map[int32]string{
		0: "HITCH_COMMAND_UNSPECIFIED",
		1: "RAISED",
		2: "LOWERED",
	}
	HitchState_HitchCommand_value = map[string]int32{
		"HITCH_COMMAND_UNSPECIFIED": 0,
		"RAISED":                    1,
		"LOWERED":                   2,
	}
)

func (x HitchState_HitchCommand) Enum() *HitchState_HitchCommand {
	p := new(HitchState_HitchCommand)
	*p = x
	return p
}

func (x HitchState_HitchCommand) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitchState_HitchCommand) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[3].Descriptor()
}

func (HitchState_HitchCommand) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[3]
}

func (x HitchState_HitchCommand) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitchState_HitchCommand.Descriptor instead.
func (HitchState_HitchCommand) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{16, 0}
}

type Job_JobType int32

const (
	Job_JOB_TYPE_UNSPECIFIED Job_JobType = 0
	Job_LASER_WEED           Job_JobType = 1
)

// Enum value maps for Job_JobType.
var (
	Job_JobType_name = map[int32]string{
		0: "JOB_TYPE_UNSPECIFIED",
		1: "LASER_WEED",
	}
	Job_JobType_value = map[string]int32{
		"JOB_TYPE_UNSPECIFIED": 0,
		"LASER_WEED":           1,
	}
)

func (x Job_JobType) Enum() *Job_JobType {
	p := new(Job_JobType)
	*p = x
	return p
}

func (x Job_JobType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Job_JobType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[4].Descriptor()
}

func (Job_JobType) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[4]
}

func (x Job_JobType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Job_JobType.Descriptor instead.
func (Job_JobType) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{20, 0}
}

type Intervention_InterventionCause int32

const (
	Intervention_INTERVENTION_CAUSE_UNSPECIFIED Intervention_InterventionCause = 0
	Intervention_SENSOR_TRIGGERED               Intervention_InterventionCause = 1
	Intervention_SAFETY_DRIVER_ACTION           Intervention_InterventionCause = 2
	Intervention_TRACTOR_REQUEST                Intervention_InterventionCause = 3
)

// Enum value maps for Intervention_InterventionCause.
var (
	Intervention_InterventionCause_name = map[int32]string{
		0: "INTERVENTION_CAUSE_UNSPECIFIED",
		1: "SENSOR_TRIGGERED",
		2: "SAFETY_DRIVER_ACTION",
		3: "TRACTOR_REQUEST",
	}
	Intervention_InterventionCause_value = map[string]int32{
		"INTERVENTION_CAUSE_UNSPECIFIED": 0,
		"SENSOR_TRIGGERED":               1,
		"SAFETY_DRIVER_ACTION":           2,
		"TRACTOR_REQUEST":                3,
	}
)

func (x Intervention_InterventionCause) Enum() *Intervention_InterventionCause {
	p := new(Intervention_InterventionCause)
	*p = x
	return p
}

func (x Intervention_InterventionCause) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Intervention_InterventionCause) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_rtc_jobs_proto_enumTypes[5].Descriptor()
}

func (Intervention_InterventionCause) Type() protoreflect.EnumType {
	return &file_proto_rtc_jobs_proto_enumTypes[5]
}

func (x Intervention_InterventionCause) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Intervention_InterventionCause.Descriptor instead.
func (Intervention_InterventionCause) EnumDescriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{26, 0}
}

// Objective API
type Objective struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description     string                  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ProgressPercent int32                   `protobuf:"varint,4,opt,name=progress_percent,json=progressPercent,proto3" json:"progress_percent,omitempty"` // (0-100)
	Priority        int32                   `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`                                      // for ordering
	Type            Objective_ObjectiveType `protobuf:"varint,6,opt,name=type,proto3,enum=carbon.rtc.Objective_ObjectiveType" json:"type,omitempty"`
	// dynamic/json to define, per objective info
	//
	//	this is nice and flexible so for example laser weeding can be a row (start
	//	-> stop) but also be more.
	Data *structpb.Struct `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Objective) Reset() {
	*x = Objective{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Objective) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Objective) ProtoMessage() {}

func (x *Objective) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Objective.ProtoReflect.Descriptor instead.
func (*Objective) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{0}
}

func (x *Objective) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Objective) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Objective) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Objective) GetProgressPercent() int32 {
	if x != nil {
		return x.ProgressPercent
	}
	return 0
}

func (x *Objective) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Objective) GetType() Objective_ObjectiveType {
	if x != nil {
		return x.Type
	}
	return Objective_OBJECTIVE_TYPE_UNSPECIFIED
}

func (x *Objective) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type ObjectiveList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Objectives []*Objective `protobuf:"bytes,1,rep,name=objectives,proto3" json:"objectives,omitempty"`
}

func (x *ObjectiveList) Reset() {
	*x = ObjectiveList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectiveList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectiveList) ProtoMessage() {}

func (x *ObjectiveList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectiveList.ProtoReflect.Descriptor instead.
func (*ObjectiveList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{1}
}

func (x *ObjectiveList) GetObjectives() []*Objective {
	if x != nil {
		return x.Objectives
	}
	return nil
}

type ListObjectivesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageToken  string       `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Objectives []*Objective `protobuf:"bytes,2,rep,name=objectives,proto3" json:"objectives,omitempty"`
}

func (x *ListObjectivesResponse) Reset() {
	*x = ListObjectivesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListObjectivesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListObjectivesResponse) ProtoMessage() {}

func (x *ListObjectivesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListObjectivesResponse.ProtoReflect.Descriptor instead.
func (*ListObjectivesResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{2}
}

func (x *ListObjectivesResponse) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListObjectivesResponse) GetObjectives() []*Objective {
	if x != nil {
		return x.Objectives
	}
	return nil
}

type GetNextActiveObjectiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ObjectiveId uint64 `protobuf:"varint,1,opt,name=objective_id,json=objectiveId,proto3" json:"objective_id,omitempty"`
}

func (x *GetNextActiveObjectiveRequest) Reset() {
	*x = GetNextActiveObjectiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveObjectiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveObjectiveRequest) ProtoMessage() {}

func (x *GetNextActiveObjectiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveObjectiveRequest.ProtoReflect.Descriptor instead.
func (*GetNextActiveObjectiveRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{3}
}

func (x *GetNextActiveObjectiveRequest) GetObjectiveId() uint64 {
	if x != nil {
		return x.ObjectiveId
	}
	return 0
}

type GetNextActiveObjectiveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Objective *Objective `protobuf:"bytes,2,opt,name=objective,proto3" json:"objective,omitempty"`
}

func (x *GetNextActiveObjectiveResponse) Reset() {
	*x = GetNextActiveObjectiveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextActiveObjectiveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextActiveObjectiveResponse) ProtoMessage() {}

func (x *GetNextActiveObjectiveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextActiveObjectiveResponse.ProtoReflect.Descriptor instead.
func (*GetNextActiveObjectiveResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{4}
}

func (x *GetNextActiveObjectiveResponse) GetObjective() *Objective {
	if x != nil {
		return x.Objective
	}
	return nil
}

// Task API
type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartedAt            *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt              *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	ExpectedDuration     *durationpb.Duration   `protobuf:"bytes,5,opt,name=expected_duration,json=expectedDuration,proto3" json:"expected_duration,omitempty"`
	StatusInfo           string                 `protobuf:"bytes,6,opt,name=status_info,json=statusInfo,proto3" json:"status_info,omitempty"`
	ExpectedTractorState []*TractorState        `protobuf:"bytes,7,rep,name=expected_tractor_state,json=expectedTractorState,proto3" json:"expected_tractor_state,omitempty"`
	State                State                  `protobuf:"varint,8,opt,name=state,proto3,enum=carbon.rtc.State" json:"state,omitempty"`
	Priority             int32                  `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`
	ObjectiveId          uint64                 `protobuf:"varint,16,opt,name=objective_id,json=objectiveId,proto3" json:"objective_id,omitempty"`
	StartLocation        *geo.Point             `protobuf:"bytes,18,opt,name=start_location,json=startLocation,proto3" json:"start_location,omitempty"`
	StartHeading         float64                `protobuf:"fixed64,19,opt,name=start_heading,json=startHeading,proto3" json:"start_heading,omitempty"`
	EndLocation          *geo.Point             `protobuf:"bytes,20,opt,name=end_location,json=endLocation,proto3" json:"end_location,omitempty"`
	EndHeading           float64                `protobuf:"fixed64,21,opt,name=end_heading,json=endHeading,proto3" json:"end_heading,omitempty"`
	// Types that are assignable to TaskDetails:
	//
	//	*Task_Sequence
	//	*Task_Manual
	//	*Task_GoToAndFace
	//	*Task_FollowPath
	//	*Task_TractorState
	//	*Task_LaserWeed
	//	*Task_StopAutonomy
	//	*Task_GoToReversiblePath
	TaskDetails isTask_TaskDetails `protobuf_oneof:"task_details"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{5}
}

func (x *Task) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Task) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Task) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *Task) GetExpectedDuration() *durationpb.Duration {
	if x != nil {
		return x.ExpectedDuration
	}
	return nil
}

func (x *Task) GetStatusInfo() string {
	if x != nil {
		return x.StatusInfo
	}
	return ""
}

func (x *Task) GetExpectedTractorState() []*TractorState {
	if x != nil {
		return x.ExpectedTractorState
	}
	return nil
}

func (x *Task) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *Task) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Task) GetObjectiveId() uint64 {
	if x != nil {
		return x.ObjectiveId
	}
	return 0
}

func (x *Task) GetStartLocation() *geo.Point {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *Task) GetStartHeading() float64 {
	if x != nil {
		return x.StartHeading
	}
	return 0
}

func (x *Task) GetEndLocation() *geo.Point {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *Task) GetEndHeading() float64 {
	if x != nil {
		return x.EndHeading
	}
	return 0
}

func (m *Task) GetTaskDetails() isTask_TaskDetails {
	if m != nil {
		return m.TaskDetails
	}
	return nil
}

func (x *Task) GetSequence() *SequenceTask {
	if x, ok := x.GetTaskDetails().(*Task_Sequence); ok {
		return x.Sequence
	}
	return nil
}

func (x *Task) GetManual() *ManualTask {
	if x, ok := x.GetTaskDetails().(*Task_Manual); ok {
		return x.Manual
	}
	return nil
}

func (x *Task) GetGoToAndFace() *GoToAndFaceTask {
	if x, ok := x.GetTaskDetails().(*Task_GoToAndFace); ok {
		return x.GoToAndFace
	}
	return nil
}

func (x *Task) GetFollowPath() *FollowPathTask {
	if x, ok := x.GetTaskDetails().(*Task_FollowPath); ok {
		return x.FollowPath
	}
	return nil
}

func (x *Task) GetTractorState() *SetTractorStateTask {
	if x, ok := x.GetTaskDetails().(*Task_TractorState); ok {
		return x.TractorState
	}
	return nil
}

func (x *Task) GetLaserWeed() *LaserWeedTask {
	if x, ok := x.GetTaskDetails().(*Task_LaserWeed); ok {
		return x.LaserWeed
	}
	return nil
}

func (x *Task) GetStopAutonomy() *StopAutonomyTask {
	if x, ok := x.GetTaskDetails().(*Task_StopAutonomy); ok {
		return x.StopAutonomy
	}
	return nil
}

func (x *Task) GetGoToReversiblePath() *GoToReversiblePathTask {
	if x, ok := x.GetTaskDetails().(*Task_GoToReversiblePath); ok {
		return x.GoToReversiblePath
	}
	return nil
}

type isTask_TaskDetails interface {
	isTask_TaskDetails()
}

type Task_Sequence struct {
	// core structural task types
	Sequence *SequenceTask `protobuf:"bytes,10,opt,name=sequence,proto3,oneof"`
}

type Task_Manual struct {
	Manual *ManualTask `protobuf:"bytes,11,opt,name=manual,proto3,oneof"`
}

type Task_GoToAndFace struct {
	// tasks with specific automations available
	GoToAndFace *GoToAndFaceTask `protobuf:"bytes,12,opt,name=go_to_and_face,json=goToAndFace,proto3,oneof"`
}

type Task_FollowPath struct {
	FollowPath *FollowPathTask `protobuf:"bytes,13,opt,name=follow_path,json=followPath,proto3,oneof"`
}

type Task_TractorState struct {
	TractorState *SetTractorStateTask `protobuf:"bytes,14,opt,name=tractor_state,json=tractorState,proto3,oneof"`
}

type Task_LaserWeed struct {
	LaserWeed *LaserWeedTask `protobuf:"bytes,15,opt,name=laser_weed,json=laserWeed,proto3,oneof"`
}

type Task_StopAutonomy struct {
	StopAutonomy *StopAutonomyTask `protobuf:"bytes,17,opt,name=stop_autonomy,json=stopAutonomy,proto3,oneof"`
}

type Task_GoToReversiblePath struct {
	GoToReversiblePath *GoToReversiblePathTask `protobuf:"bytes,22,opt,name=go_to_reversible_path,json=goToReversiblePath,proto3,oneof"`
}

func (*Task_Sequence) isTask_TaskDetails() {}

func (*Task_Manual) isTask_TaskDetails() {}

func (*Task_GoToAndFace) isTask_TaskDetails() {}

func (*Task_FollowPath) isTask_TaskDetails() {}

func (*Task_TractorState) isTask_TaskDetails() {}

func (*Task_LaserWeed) isTask_TaskDetails() {}

func (*Task_StopAutonomy) isTask_TaskDetails() {}

func (*Task_GoToReversiblePath) isTask_TaskDetails() {}

// A `StopAutonomyTask` is to signal completion, allowing the robot to exit
// autonomy mode and do nothing.
type StopAutonomyTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopAutonomyTask) Reset() {
	*x = StopAutonomyTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopAutonomyTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopAutonomyTask) ProtoMessage() {}

func (x *StopAutonomyTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopAutonomyTask.ProtoReflect.Descriptor instead.
func (*StopAutonomyTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{6}
}

type LaserWeedTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start as a path / row
	Path             *geo.LineString       `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	PathIsReversible bool                  `protobuf:"varint,2,opt,name=path_is_reversible,json=pathIsReversible,proto3" json:"path_is_reversible,omitempty"`
	WeedingEnabled   bool                  `protobuf:"varint,3,opt,name=weeding_enabled,json=weedingEnabled,proto3" json:"weeding_enabled,omitempty"`
	ThinningEnabled  bool                  `protobuf:"varint,4,opt,name=thinning_enabled,json=thinningEnabled,proto3" json:"thinning_enabled,omitempty"`
	Manual           bool                  `protobuf:"varint,5,opt,name=manual,proto3" json:"manual,omitempty"`
	Tolerances       *SpatialPathTolerance `protobuf:"bytes,6,opt,name=tolerances,proto3" json:"tolerances,omitempty"`
}

func (x *LaserWeedTask) Reset() {
	*x = LaserWeedTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaserWeedTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaserWeedTask) ProtoMessage() {}

func (x *LaserWeedTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaserWeedTask.ProtoReflect.Descriptor instead.
func (*LaserWeedTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{7}
}

func (x *LaserWeedTask) GetPath() *geo.LineString {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *LaserWeedTask) GetPathIsReversible() bool {
	if x != nil {
		return x.PathIsReversible
	}
	return false
}

func (x *LaserWeedTask) GetWeedingEnabled() bool {
	if x != nil {
		return x.WeedingEnabled
	}
	return false
}

func (x *LaserWeedTask) GetThinningEnabled() bool {
	if x != nil {
		return x.ThinningEnabled
	}
	return false
}

func (x *LaserWeedTask) GetManual() bool {
	if x != nil {
		return x.Manual
	}
	return false
}

func (x *LaserWeedTask) GetTolerances() *SpatialPathTolerance {
	if x != nil {
		return x.Tolerances
	}
	return nil
}

// A `SequenceTask` requires completing all of its child tasks, in order, with
// no overlap in time.
type SequenceTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items  []*Task `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Atomic bool    `protobuf:"varint,2,opt,name=atomic,proto3" json:"atomic,omitempty"`
}

func (x *SequenceTask) Reset() {
	*x = SequenceTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SequenceTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SequenceTask) ProtoMessage() {}

func (x *SequenceTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SequenceTask.ProtoReflect.Descriptor instead.
func (*SequenceTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{8}
}

func (x *SequenceTask) GetItems() []*Task {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *SequenceTask) GetAtomic() bool {
	if x != nil {
		return x.Atomic
	}
	return false
}

// A `ManualTask` can only be completed by a human and will always require an
// intervention request.
type ManualTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Instructions string `protobuf:"bytes,1,opt,name=instructions,proto3" json:"instructions,omitempty"`
}

func (x *ManualTask) Reset() {
	*x = ManualTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualTask) ProtoMessage() {}

func (x *ManualTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualTask.ProtoReflect.Descriptor instead.
func (*ManualTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{9}
}

func (x *ManualTask) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

// A `GoToAndFaceTask` requires that the robot move to a particular position
// and face in a particular direction. For example, this might be used to line
// up at the start of a row.
type GoToAndFaceTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Point   *geo.Point `protobuf:"bytes,1,opt,name=point,proto3" json:"point,omitempty"`
	Heading float64    `protobuf:"fixed64,2,opt,name=heading,proto3" json:"heading,omitempty"`
}

func (x *GoToAndFaceTask) Reset() {
	*x = GoToAndFaceTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoToAndFaceTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoToAndFaceTask) ProtoMessage() {}

func (x *GoToAndFaceTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoToAndFaceTask.ProtoReflect.Descriptor instead.
func (*GoToAndFaceTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{10}
}

func (x *GoToAndFaceTask) GetPoint() *geo.Point {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *GoToAndFaceTask) GetHeading() float64 {
	if x != nil {
		return x.Heading
	}
	return 0
}

// A `GoToReversiblePathTask` requires that the robot move to one end of a path,
// facing the other end of the first path segment. This is useful for lining
// up at either end of a row (whichever is most convenient), or the start of a
// more complex path like a ground prep pass.
type GoToReversiblePathTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path       *geo.LineString       `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Tolerances *SpatialPathTolerance `protobuf:"bytes,2,opt,name=tolerances,proto3" json:"tolerances,omitempty"`
}

func (x *GoToReversiblePathTask) Reset() {
	*x = GoToReversiblePathTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoToReversiblePathTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoToReversiblePathTask) ProtoMessage() {}

func (x *GoToReversiblePathTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoToReversiblePathTask.ProtoReflect.Descriptor instead.
func (*GoToReversiblePathTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{11}
}

func (x *GoToReversiblePathTask) GetPath() *geo.LineString {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *GoToReversiblePathTask) GetTolerances() *SpatialPathTolerance {
	if x != nil {
		return x.Tolerances
	}
	return nil
}

type FollowPathTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path             *geo.LineString `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Speed            *SpeedSetting   `protobuf:"bytes,2,opt,name=speed,proto3" json:"speed,omitempty"`
	StopOnCompletion bool            `protobuf:"varint,3,opt,name=stop_on_completion,json=stopOnCompletion,proto3" json:"stop_on_completion,omitempty"`
}

func (x *FollowPathTask) Reset() {
	*x = FollowPathTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowPathTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowPathTask) ProtoMessage() {}

func (x *FollowPathTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowPathTask.ProtoReflect.Descriptor instead.
func (*FollowPathTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{12}
}

func (x *FollowPathTask) GetPath() *geo.LineString {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *FollowPathTask) GetSpeed() *SpeedSetting {
	if x != nil {
		return x.Speed
	}
	return nil
}

func (x *FollowPathTask) GetStopOnCompletion() bool {
	if x != nil {
		return x.StopOnCompletion
	}
	return false
}

type SpeedSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Speed:
	//
	//	*SpeedSetting_ConstantMph
	//	*SpeedSetting_RemoteOperatorControlled
	//	*SpeedSetting_ImplementControlled
	Speed isSpeedSetting_Speed `protobuf_oneof:"speed"`
}

func (x *SpeedSetting) Reset() {
	*x = SpeedSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedSetting) ProtoMessage() {}

func (x *SpeedSetting) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedSetting.ProtoReflect.Descriptor instead.
func (*SpeedSetting) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{13}
}

func (m *SpeedSetting) GetSpeed() isSpeedSetting_Speed {
	if m != nil {
		return m.Speed
	}
	return nil
}

func (x *SpeedSetting) GetConstantMph() float64 {
	if x, ok := x.GetSpeed().(*SpeedSetting_ConstantMph); ok {
		return x.ConstantMph
	}
	return 0
}

func (x *SpeedSetting) GetRemoteOperatorControlled() *emptypb.Empty {
	if x, ok := x.GetSpeed().(*SpeedSetting_RemoteOperatorControlled); ok {
		return x.RemoteOperatorControlled
	}
	return nil
}

func (x *SpeedSetting) GetImplementControlled() *emptypb.Empty {
	if x, ok := x.GetSpeed().(*SpeedSetting_ImplementControlled); ok {
		return x.ImplementControlled
	}
	return nil
}

type isSpeedSetting_Speed interface {
	isSpeedSetting_Speed()
}

type SpeedSetting_ConstantMph struct {
	ConstantMph float64 `protobuf:"fixed64,1,opt,name=constant_mph,json=constantMph,proto3,oneof"`
}

type SpeedSetting_RemoteOperatorControlled struct {
	RemoteOperatorControlled *emptypb.Empty `protobuf:"bytes,2,opt,name=remote_operator_controlled,json=remoteOperatorControlled,proto3,oneof"`
}

type SpeedSetting_ImplementControlled struct {
	ImplementControlled *emptypb.Empty `protobuf:"bytes,3,opt,name=implement_controlled,json=implementControlled,proto3,oneof"`
}

func (*SpeedSetting_ConstantMph) isSpeedSetting_Speed() {}

func (*SpeedSetting_RemoteOperatorControlled) isSpeedSetting_Speed() {}

func (*SpeedSetting_ImplementControlled) isSpeedSetting_Speed() {}

type SetTractorStateTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State []*TractorState `protobuf:"bytes,1,rep,name=state,proto3" json:"state,omitempty"`
}

func (x *SetTractorStateTask) Reset() {
	*x = SetTractorStateTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTractorStateTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTractorStateTask) ProtoMessage() {}

func (x *SetTractorStateTask) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTractorStateTask.ProtoReflect.Descriptor instead.
func (*SetTractorStateTask) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{14}
}

func (x *SetTractorStateTask) GetState() []*TractorState {
	if x != nil {
		return x.State
	}
	return nil
}

type TractorState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to State:
	//
	//	*TractorState_Gear_
	//	*TractorState_Hitch
	State isTractorState_State `protobuf_oneof:"state"`
}

func (x *TractorState) Reset() {
	*x = TractorState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorState) ProtoMessage() {}

func (x *TractorState) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorState.ProtoReflect.Descriptor instead.
func (*TractorState) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{15}
}

func (m *TractorState) GetState() isTractorState_State {
	if m != nil {
		return m.State
	}
	return nil
}

func (x *TractorState) GetGear() TractorState_Gear {
	if x, ok := x.GetState().(*TractorState_Gear_); ok {
		return x.Gear
	}
	return TractorState_GEAR_UNSPECIFIED
}

func (x *TractorState) GetHitch() *HitchState {
	if x, ok := x.GetState().(*TractorState_Hitch); ok {
		return x.Hitch
	}
	return nil
}

type isTractorState_State interface {
	isTractorState_State()
}

type TractorState_Gear_ struct {
	Gear TractorState_Gear `protobuf:"varint,1,opt,name=gear,proto3,enum=carbon.rtc.TractorState_Gear,oneof"`
}

type TractorState_Hitch struct {
	Hitch *HitchState `protobuf:"bytes,2,opt,name=hitch,proto3,oneof"`
}

func (*TractorState_Gear_) isTractorState_State() {}

func (*TractorState_Hitch) isTractorState_State() {}

type HitchState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to State:
	//
	//	*HitchState_Command
	//	*HitchState_Position
	State isHitchState_State `protobuf_oneof:"state"`
}

func (x *HitchState) Reset() {
	*x = HitchState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HitchState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HitchState) ProtoMessage() {}

func (x *HitchState) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HitchState.ProtoReflect.Descriptor instead.
func (*HitchState) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{16}
}

func (m *HitchState) GetState() isHitchState_State {
	if m != nil {
		return m.State
	}
	return nil
}

func (x *HitchState) GetCommand() HitchState_HitchCommand {
	if x, ok := x.GetState().(*HitchState_Command); ok {
		return x.Command
	}
	return HitchState_HITCH_COMMAND_UNSPECIFIED
}

func (x *HitchState) GetPosition() float64 {
	if x, ok := x.GetState().(*HitchState_Position); ok {
		return x.Position
	}
	return 0
}

type isHitchState_State interface {
	isHitchState_State()
}

type HitchState_Command struct {
	Command HitchState_HitchCommand `protobuf:"varint,1,opt,name=command,proto3,enum=carbon.rtc.HitchState_HitchCommand,oneof"`
}

type HitchState_Position struct {
	// Target position, between 0.0 (fully lowered) and 1.0 (fully raised).
	Position float64 `protobuf:"fixed64,2,opt,name=position,proto3,oneof"`
}

func (*HitchState_Command) isHitchState_State() {}

func (*HitchState_Position) isHitchState_State() {}

type TaskList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*Task `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *TaskList) Reset() {
	*x = TaskList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskList) ProtoMessage() {}

func (x *TaskList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskList.ProtoReflect.Descriptor instead.
func (*TaskList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{17}
}

func (x *TaskList) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type ListTasksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageToken string  `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Tasks     []*Task `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *ListTasksResponse) Reset() {
	*x = ListTasksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTasksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTasksResponse) ProtoMessage() {}

func (x *ListTasksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTasksResponse.ProtoReflect.Descriptor instead.
func (*ListTasksResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{18}
}

func (x *ListTasksResponse) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListTasksResponse) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// SpatialPathTolerance is a set of tolerance values used to evaluate geo
// spatial criteria based on location and/or heading.
type SpatialPathTolerance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Heading              float32 `protobuf:"fixed32,1,opt,name=heading,proto3" json:"heading,omitempty"`
	Crosstrack           float32 `protobuf:"fixed32,2,opt,name=crosstrack,proto3" json:"crosstrack,omitempty"`
	Distance             float32 `protobuf:"fixed32,3,opt,name=distance,proto3" json:"distance,omitempty"`
	ContinuousCrosstrack float32 `protobuf:"fixed32,4,opt,name=continuous_crosstrack,json=continuousCrosstrack,proto3" json:"continuous_crosstrack,omitempty"`
}

func (x *SpatialPathTolerance) Reset() {
	*x = SpatialPathTolerance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpatialPathTolerance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpatialPathTolerance) ProtoMessage() {}

func (x *SpatialPathTolerance) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpatialPathTolerance.ProtoReflect.Descriptor instead.
func (*SpatialPathTolerance) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{19}
}

func (x *SpatialPathTolerance) GetHeading() float32 {
	if x != nil {
		return x.Heading
	}
	return 0
}

func (x *SpatialPathTolerance) GetCrosstrack() float32 {
	if x != nil {
		return x.Crosstrack
	}
	return 0
}

func (x *SpatialPathTolerance) GetDistance() float32 {
	if x != nil {
		return x.Distance
	}
	return 0
}

func (x *SpatialPathTolerance) GetContinuousCrosstrack() float32 {
	if x != nil {
		return x.ContinuousCrosstrack
	}
	return 0
}

// Jobs API
type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartedAt   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Objectives  []*Objective           `protobuf:"bytes,5,rep,name=objectives,proto3" json:"objectives,omitempty"`
	State       State                  `protobuf:"varint,6,opt,name=state,proto3,enum=carbon.rtc.State" json:"state,omitempty"`
	Type        Job_JobType            `protobuf:"varint,7,opt,name=type,proto3,enum=carbon.rtc.Job_JobType" json:"type,omitempty"`
	WorkOrderId *uint64                `protobuf:"varint,8,opt,name=work_order_id,json=workOrderId,proto3,oneof" json:"work_order_id,omitempty"`
	// Farm ID per Portal, typically a 12-character alphanumeric string.
	FarmId  string `protobuf:"bytes,9,opt,name=farm_id,json=farmId,proto3" json:"farm_id,omitempty"`
	FieldId string `protobuf:"bytes,10,opt,name=field_id,json=fieldId,proto3" json:"field_id,omitempty"`
	// Customer ID per Portal, typically a UUID.
	CustomerId string `protobuf:"bytes,11,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Priority   int32  `protobuf:"varint,12,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{20}
}

func (x *Job) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Job) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *Job) GetObjectives() []*Objective {
	if x != nil {
		return x.Objectives
	}
	return nil
}

func (x *Job) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *Job) GetType() Job_JobType {
	if x != nil {
		return x.Type
	}
	return Job_JOB_TYPE_UNSPECIFIED
}

func (x *Job) GetWorkOrderId() uint64 {
	if x != nil && x.WorkOrderId != nil {
		return *x.WorkOrderId
	}
	return 0
}

func (x *Job) GetFarmId() string {
	if x != nil {
		return x.FarmId
	}
	return ""
}

func (x *Job) GetFieldId() string {
	if x != nil {
		return x.FieldId
	}
	return ""
}

func (x *Job) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *Job) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type JobList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs []*Job `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
}

func (x *JobList) Reset() {
	*x = JobList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobList) ProtoMessage() {}

func (x *JobList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobList.ProtoReflect.Descriptor instead.
func (*JobList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{21}
}

func (x *JobList) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type ListJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageToken string `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Jobs      []*Job `protobuf:"bytes,2,rep,name=jobs,proto3" json:"jobs,omitempty"`
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{22}
}

func (x *ListJobsResponse) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

// Work Orders APIs
type WorkOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ScheduledAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	DurationMinutes int32                  `protobuf:"varint,4,opt,name=duration_minutes,json=durationMinutes,proto3" json:"duration_minutes,omitempty"`
	Jobs            []*Job                 `protobuf:"bytes,5,rep,name=jobs,proto3" json:"jobs,omitempty"`
	State           State                  `protobuf:"varint,6,opt,name=state,proto3,enum=carbon.rtc.State" json:"state,omitempty"`
}

func (x *WorkOrder) Reset() {
	*x = WorkOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkOrder) ProtoMessage() {}

func (x *WorkOrder) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkOrder.ProtoReflect.Descriptor instead.
func (*WorkOrder) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{23}
}

func (x *WorkOrder) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkOrder) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkOrder) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *WorkOrder) GetDurationMinutes() int32 {
	if x != nil {
		return x.DurationMinutes
	}
	return 0
}

func (x *WorkOrder) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *WorkOrder) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

type WorkOrderList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkOrders []*WorkOrder `protobuf:"bytes,1,rep,name=work_orders,json=workOrders,proto3" json:"work_orders,omitempty"`
}

func (x *WorkOrderList) Reset() {
	*x = WorkOrderList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkOrderList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkOrderList) ProtoMessage() {}

func (x *WorkOrderList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkOrderList.ProtoReflect.Descriptor instead.
func (*WorkOrderList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{24}
}

func (x *WorkOrderList) GetWorkOrders() []*WorkOrder {
	if x != nil {
		return x.WorkOrders
	}
	return nil
}

type ListWorkOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageToken  string       `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	WorkOrders []*WorkOrder `protobuf:"bytes,2,rep,name=work_orders,json=workOrders,proto3" json:"work_orders,omitempty"`
}

func (x *ListWorkOrdersResponse) Reset() {
	*x = ListWorkOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWorkOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkOrdersResponse) ProtoMessage() {}

func (x *ListWorkOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkOrdersResponse.ProtoReflect.Descriptor instead.
func (*ListWorkOrdersResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{25}
}

func (x *ListWorkOrdersResponse) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListWorkOrdersResponse) GetWorkOrders() []*WorkOrder {
	if x != nil {
		return x.WorkOrders
	}
	return nil
}

// Intervention APIs
type Intervention struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint64                         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TaskId        uint64                         `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Qualification string                         `protobuf:"bytes,3,opt,name=qualification,proto3" json:"qualification,omitempty"`
	Description   string                         `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	State         State                          `protobuf:"varint,5,opt,name=state,proto3,enum=carbon.rtc.State" json:"state,omitempty"`
	RobotSerial   string                         `protobuf:"bytes,6,opt,name=robot_serial,json=robotSerial,proto3" json:"robot_serial,omitempty"`
	JobId         uint64                         `protobuf:"varint,7,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	Cause         Intervention_InterventionCause `protobuf:"varint,8,opt,name=cause,proto3,enum=carbon.rtc.Intervention_InterventionCause" json:"cause,omitempty"`
	Priority      int32                          `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *Intervention) Reset() {
	*x = Intervention{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Intervention) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Intervention) ProtoMessage() {}

func (x *Intervention) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Intervention.ProtoReflect.Descriptor instead.
func (*Intervention) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{26}
}

func (x *Intervention) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Intervention) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *Intervention) GetQualification() string {
	if x != nil {
		return x.Qualification
	}
	return ""
}

func (x *Intervention) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Intervention) GetState() State {
	if x != nil {
		return x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *Intervention) GetRobotSerial() string {
	if x != nil {
		return x.RobotSerial
	}
	return ""
}

func (x *Intervention) GetJobId() uint64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

func (x *Intervention) GetCause() Intervention_InterventionCause {
	if x != nil {
		return x.Cause
	}
	return Intervention_INTERVENTION_CAUSE_UNSPECIFIED
}

func (x *Intervention) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type InterventionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intervention []*Intervention `protobuf:"bytes,1,rep,name=intervention,proto3" json:"intervention,omitempty"`
}

func (x *InterventionList) Reset() {
	*x = InterventionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterventionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterventionList) ProtoMessage() {}

func (x *InterventionList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterventionList.ProtoReflect.Descriptor instead.
func (*InterventionList) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{27}
}

func (x *InterventionList) GetIntervention() []*Intervention {
	if x != nil {
		return x.Intervention
	}
	return nil
}

type ListInterventionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize  int32  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"` // TODO:(smt) query filters
}

func (x *ListInterventionsRequest) Reset() {
	*x = ListInterventionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInterventionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInterventionsRequest) ProtoMessage() {}

func (x *ListInterventionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInterventionsRequest.ProtoReflect.Descriptor instead.
func (*ListInterventionsRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{28}
}

func (x *ListInterventionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListInterventionsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListInterventionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageToken     string          `protobuf:"bytes,1,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Interventions []*Intervention `protobuf:"bytes,2,rep,name=interventions,proto3" json:"interventions,omitempty"`
}

func (x *ListInterventionsResponse) Reset() {
	*x = ListInterventionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInterventionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInterventionsResponse) ProtoMessage() {}

func (x *ListInterventionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInterventionsResponse.ProtoReflect.Descriptor instead.
func (*ListInterventionsResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{29}
}

func (x *ListInterventionsResponse) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListInterventionsResponse) GetInterventions() []*Intervention {
	if x != nil {
		return x.Interventions
	}
	return nil
}

type CreateInterventionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intervention *Intervention `protobuf:"bytes,1,opt,name=intervention,proto3" json:"intervention,omitempty"`
}

func (x *CreateInterventionRequest) Reset() {
	*x = CreateInterventionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInterventionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInterventionRequest) ProtoMessage() {}

func (x *CreateInterventionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInterventionRequest.ProtoReflect.Descriptor instead.
func (*CreateInterventionRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{30}
}

func (x *CreateInterventionRequest) GetIntervention() *Intervention {
	if x != nil {
		return x.Intervention
	}
	return nil
}

type CreateInterventionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intervention *Intervention `protobuf:"bytes,1,opt,name=intervention,proto3" json:"intervention,omitempty"`
}

func (x *CreateInterventionResponse) Reset() {
	*x = CreateInterventionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInterventionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInterventionResponse) ProtoMessage() {}

func (x *CreateInterventionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInterventionResponse.ProtoReflect.Descriptor instead.
func (*CreateInterventionResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{31}
}

func (x *CreateInterventionResponse) GetIntervention() *Intervention {
	if x != nil {
		return x.Intervention
	}
	return nil
}

type GetActiveTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentLocation *geo.Point `protobuf:"bytes,2,opt,name=current_location,json=currentLocation,proto3" json:"current_location,omitempty"` // TODO:(smt) what other state should we provide?
}

func (x *GetActiveTaskRequest) Reset() {
	*x = GetActiveTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveTaskRequest) ProtoMessage() {}

func (x *GetActiveTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveTaskRequest.ProtoReflect.Descriptor instead.
func (*GetActiveTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{32}
}

func (x *GetActiveTaskRequest) GetCurrentLocation() *geo.Point {
	if x != nil {
		return x.CurrentLocation
	}
	return nil
}

type GetActiveTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Task *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
}

func (x *GetActiveTaskResponse) Reset() {
	*x = GetActiveTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActiveTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveTaskResponse) ProtoMessage() {}

func (x *GetActiveTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveTaskResponse.ProtoReflect.Descriptor instead.
func (*GetActiveTaskResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{33}
}

func (x *GetActiveTaskResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

type GetTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId uint64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *GetTaskRequest) Reset() {
	*x = GetTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRequest) ProtoMessage() {}

func (x *GetTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRequest.ProtoReflect.Descriptor instead.
func (*GetTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{34}
}

func (x *GetTaskRequest) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type GetTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Task *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
}

func (x *GetTaskResponse) Reset() {
	*x = GetTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskResponse) ProtoMessage() {}

func (x *GetTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskResponse.ProtoReflect.Descriptor instead.
func (*GetTaskResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{35}
}

func (x *GetTaskResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

type UpdateTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        uint64                 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	State         *State                 `protobuf:"varint,2,opt,name=state,proto3,enum=carbon.rtc.State,oneof" json:"state,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3,oneof" json:"started_at,omitempty"`
	StartLocation *geo.Point             `protobuf:"bytes,4,opt,name=start_location,json=startLocation,proto3,oneof" json:"start_location,omitempty"`
	StartHeading  *float64               `protobuf:"fixed64,5,opt,name=start_heading,json=startHeading,proto3,oneof" json:"start_heading,omitempty"`
	EndedAt       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=ended_at,json=endedAt,proto3,oneof" json:"ended_at,omitempty"`
	EndLocation   *geo.Point             `protobuf:"bytes,7,opt,name=end_location,json=endLocation,proto3,oneof" json:"end_location,omitempty"`
	EndHeading    *float64               `protobuf:"fixed64,8,opt,name=end_heading,json=endHeading,proto3,oneof" json:"end_heading,omitempty"`
	StatusInfo    *string                `protobuf:"bytes,9,opt,name=status_info,json=statusInfo,proto3,oneof" json:"status_info,omitempty"`
}

func (x *UpdateTaskRequest) Reset() {
	*x = UpdateTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskRequest) ProtoMessage() {}

func (x *UpdateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateTaskRequest) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateTaskRequest) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateTaskRequest) GetState() State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return State_STATE_UNSPECIFIED
}

func (x *UpdateTaskRequest) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *UpdateTaskRequest) GetStartLocation() *geo.Point {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *UpdateTaskRequest) GetStartHeading() float64 {
	if x != nil && x.StartHeading != nil {
		return *x.StartHeading
	}
	return 0
}

func (x *UpdateTaskRequest) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *UpdateTaskRequest) GetEndLocation() *geo.Point {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *UpdateTaskRequest) GetEndHeading() float64 {
	if x != nil && x.EndHeading != nil {
		return *x.EndHeading
	}
	return 0
}

func (x *UpdateTaskRequest) GetStatusInfo() string {
	if x != nil && x.StatusInfo != nil {
		return *x.StatusInfo
	}
	return ""
}

type UpdateTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Task *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
}

func (x *UpdateTaskResponse) Reset() {
	*x = UpdateTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_rtc_jobs_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskResponse) ProtoMessage() {}

func (x *UpdateTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_rtc_jobs_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskResponse.ProtoReflect.Descriptor instead.
func (*UpdateTaskResponse) Descriptor() ([]byte, []int) {
	return file_proto_rtc_jobs_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateTaskResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

var File_proto_rtc_jobs_proto protoreflect.FileDescriptor

var file_proto_rtc_jobs_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x2f, 0x6a, 0x6f, 0x62, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x67, 0x65, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x02, 0x0a, 0x09, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x37,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x43, 0x0a, 0x0d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x5f, 0x57,
	0x45, 0x45, 0x44, 0x5f, 0x52, 0x4f, 0x57, 0x10, 0x01, 0x22, 0x46, 0x0a, 0x0d, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x73, 0x22, 0x6e, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x35, 0x0a, 0x0a, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x73, 0x22, 0x42, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x92, 0x09, 0x0a,
	0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x11, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4e, 0x0a, 0x16, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x14,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65,
	0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x0a, 0x0c, 0x65,
	0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x36, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00,
	0x52, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x48, 0x00, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x0e,
	0x67, 0x6f, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x47, 0x6f, 0x54, 0x6f, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x63, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x48, 0x00, 0x52, 0x0b, 0x67, 0x6f, 0x54, 0x6f, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x63, 0x65,
	0x12, 0x3d, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x74, 0x68, 0x54, 0x61, 0x73,
	0x6b, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x46, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x65, 0x72,
	0x5f, 0x77, 0x65, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x57, 0x65,
	0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x6e, 0x6f, 0x6d, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x75, 0x74, 0x6f,
	0x6e, 0x6f, 0x6d, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x70,
	0x41, 0x75, 0x74, 0x6f, 0x6e, 0x6f, 0x6d, 0x79, 0x12, 0x57, 0x0a, 0x15, 0x67, 0x6f, 0x5f, 0x74,
	0x6f, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x6f, 0x54, 0x6f, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x62, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x12, 0x67,
	0x6f, 0x54, 0x6f, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x42, 0x0e, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x6e, 0x6f, 0x6d,
	0x79, 0x54, 0x61, 0x73, 0x6b, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67,
	0x65, 0x6f, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x69, 0x73, 0x5f, 0x72,
	0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x70, 0x61, 0x74, 0x68, 0x49, 0x73, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x77, 0x65, 0x65, 0x64,
	0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x68,
	0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x12, 0x40, 0x0a,
	0x0a, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53,
	0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x0a, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22,
	0x4e, 0x0a, 0x0c, 0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x26, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x74, 0x6f, 0x6d, 0x69,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x22,
	0x30, 0x0a, 0x0a, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x22, 0x0a,
	0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x54, 0x0a, 0x0f, 0x47, 0x6f, 0x54, 0x6f, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x63, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f,
	0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x86, 0x01, 0x0a, 0x16, 0x47, 0x6f, 0x54, 0x6f,
	0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x4c, 0x69,
	0x6e, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x40,
	0x0a, 0x0a, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e,
	0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x54, 0x6f, 0x6c, 0x65, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x22, 0x9a, 0x01, 0x0a, 0x0e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x74, 0x68, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x4c,
	0x69, 0x6e, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x2e, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x74, 0x6f,
	0x70, 0x4f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe1, 0x01,
	0x0a, 0x0c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x23,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x70, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x4d, 0x70, 0x68, 0x12, 0x56, 0x0a, 0x1a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x18, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x12, 0x4b, 0x0a, 0x14, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x48, 0x00, 0x52, 0x13, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x22, 0x45, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xda, 0x01, 0x0a, 0x0c, 0x54, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x67, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x12, 0x2e,
	0x0a, 0x05, 0x68, 0x69, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x05, 0x68, 0x69, 0x74, 0x63, 0x68, 0x22, 0x5c,
	0x0a, 0x04, 0x47, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x50, 0x41, 0x52, 0x4b, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53,
	0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x55, 0x54, 0x52, 0x41, 0x4c, 0x10, 0x03,
	0x12, 0x0b, 0x0a, 0x07, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52, 0x44, 0x10, 0x04, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x05, 0x42, 0x07, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x0a, 0x48, 0x69, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x48, 0x69,
	0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x46, 0x0a, 0x0c, 0x48, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x19, 0x48, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x41, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x45, 0x44, 0x10, 0x02, 0x42, 0x07, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x32, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x5a, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x05,
	0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74,
	0x61, 0x73, 0x6b, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x14, 0x53, 0x70, 0x61, 0x74, 0x69, 0x61, 0x6c,
	0x50, 0x61, 0x74, 0x68, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x6f, 0x73, 0x73,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x72, 0x6f,
	0x73, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75,
	0x73, 0x5f, 0x63, 0x72, 0x6f, 0x73, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x22, 0x89, 0x04, 0x0a, 0x03, 0x4a, 0x6f, 0x62,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x27, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b, 0x77, 0x6f,
	0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x07,
	0x66, 0x61, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x61, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x33, 0x0a,
	0x07, 0x4a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x41, 0x53, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x45, 0x44,
	0x10, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x23, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04,
	0x6a, 0x6f, 0x62, 0x73, 0x22, 0x56, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x22, 0xe7, 0x01, 0x0a,
	0x09, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d,
	0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x29, 0x0a,
	0x10, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x04, 0x6a, 0x6f, 0x62, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x12, 0x27, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x47, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22,
	0x6f, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x36, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x22, 0xbe, 0x03, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x15,
	0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x05, 0x63, 0x61, 0x75, 0x73, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x75, 0x73, 0x65,
	0x52, 0x05, 0x63, 0x61, 0x75, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x22, 0x7c, 0x0a, 0x11, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x75, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x55, 0x53, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x41, 0x46, 0x45, 0x54, 0x59, 0x5f, 0x44, 0x52, 0x49,
	0x56, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10,
	0x03, 0x22, 0x50, 0x0a, 0x10, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x56, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7a, 0x0a, 0x19, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3e, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x59, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x5a, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3c, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x54,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3d, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a,
	0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x74,
	0x61, 0x73, 0x6b, 0x22, 0x29, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x37,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0xc2, 0x04, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72,
	0x74, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x48,
	0x02, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0c, 0x65, 0x6e, 0x64,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x48, 0x05, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x06, 0x52, 0x0a, 0x65, 0x6e, 0x64,
	0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x07, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x3a, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x2a, 0x98, 0x01, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10,
	0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x05,
	0x12, 0x0a, 0x0a, 0x06, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x4b, 0x4e,
	0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x44, 0x10, 0x08, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x45,
	0x57, 0x10, 0x09, 0x32, 0xc9, 0x03, 0x0a, 0x0a, 0x4a, 0x6f, 0x62, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x63, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x61, 0x72,
	0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a,
	0x07, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f,
	0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x29, 0x2e, 0x63, 0x61,
	0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e,
	0x72, 0x74, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x1d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x72, 0x74, 0x63, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x0b, 0x5a, 0x09, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x72, 0x74, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_rtc_jobs_proto_rawDescOnce sync.Once
	file_proto_rtc_jobs_proto_rawDescData = file_proto_rtc_jobs_proto_rawDesc
)

func file_proto_rtc_jobs_proto_rawDescGZIP() []byte {
	file_proto_rtc_jobs_proto_rawDescOnce.Do(func() {
		file_proto_rtc_jobs_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_rtc_jobs_proto_rawDescData)
	})
	return file_proto_rtc_jobs_proto_rawDescData
}

var file_proto_rtc_jobs_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_proto_rtc_jobs_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_proto_rtc_jobs_proto_goTypes = []interface{}{
	(State)(0),                             // 0: carbon.rtc.State
	(Objective_ObjectiveType)(0),           // 1: carbon.rtc.Objective.ObjectiveType
	(TractorState_Gear)(0),                 // 2: carbon.rtc.TractorState.Gear
	(HitchState_HitchCommand)(0),           // 3: carbon.rtc.HitchState.HitchCommand
	(Job_JobType)(0),                       // 4: carbon.rtc.Job.JobType
	(Intervention_InterventionCause)(0),    // 5: carbon.rtc.Intervention.InterventionCause
	(*Objective)(nil),                      // 6: carbon.rtc.Objective
	(*ObjectiveList)(nil),                  // 7: carbon.rtc.ObjectiveList
	(*ListObjectivesResponse)(nil),         // 8: carbon.rtc.ListObjectivesResponse
	(*GetNextActiveObjectiveRequest)(nil),  // 9: carbon.rtc.GetNextActiveObjectiveRequest
	(*GetNextActiveObjectiveResponse)(nil), // 10: carbon.rtc.GetNextActiveObjectiveResponse
	(*Task)(nil),                           // 11: carbon.rtc.Task
	(*StopAutonomyTask)(nil),               // 12: carbon.rtc.StopAutonomyTask
	(*LaserWeedTask)(nil),                  // 13: carbon.rtc.LaserWeedTask
	(*SequenceTask)(nil),                   // 14: carbon.rtc.SequenceTask
	(*ManualTask)(nil),                     // 15: carbon.rtc.ManualTask
	(*GoToAndFaceTask)(nil),                // 16: carbon.rtc.GoToAndFaceTask
	(*GoToReversiblePathTask)(nil),         // 17: carbon.rtc.GoToReversiblePathTask
	(*FollowPathTask)(nil),                 // 18: carbon.rtc.FollowPathTask
	(*SpeedSetting)(nil),                   // 19: carbon.rtc.SpeedSetting
	(*SetTractorStateTask)(nil),            // 20: carbon.rtc.SetTractorStateTask
	(*TractorState)(nil),                   // 21: carbon.rtc.TractorState
	(*HitchState)(nil),                     // 22: carbon.rtc.HitchState
	(*TaskList)(nil),                       // 23: carbon.rtc.TaskList
	(*ListTasksResponse)(nil),              // 24: carbon.rtc.ListTasksResponse
	(*SpatialPathTolerance)(nil),           // 25: carbon.rtc.SpatialPathTolerance
	(*Job)(nil),                            // 26: carbon.rtc.Job
	(*JobList)(nil),                        // 27: carbon.rtc.JobList
	(*ListJobsResponse)(nil),               // 28: carbon.rtc.ListJobsResponse
	(*WorkOrder)(nil),                      // 29: carbon.rtc.WorkOrder
	(*WorkOrderList)(nil),                  // 30: carbon.rtc.WorkOrderList
	(*ListWorkOrdersResponse)(nil),         // 31: carbon.rtc.ListWorkOrdersResponse
	(*Intervention)(nil),                   // 32: carbon.rtc.Intervention
	(*InterventionList)(nil),               // 33: carbon.rtc.InterventionList
	(*ListInterventionsRequest)(nil),       // 34: carbon.rtc.ListInterventionsRequest
	(*ListInterventionsResponse)(nil),      // 35: carbon.rtc.ListInterventionsResponse
	(*CreateInterventionRequest)(nil),      // 36: carbon.rtc.CreateInterventionRequest
	(*CreateInterventionResponse)(nil),     // 37: carbon.rtc.CreateInterventionResponse
	(*GetActiveTaskRequest)(nil),           // 38: carbon.rtc.GetActiveTaskRequest
	(*GetActiveTaskResponse)(nil),          // 39: carbon.rtc.GetActiveTaskResponse
	(*GetTaskRequest)(nil),                 // 40: carbon.rtc.GetTaskRequest
	(*GetTaskResponse)(nil),                // 41: carbon.rtc.GetTaskResponse
	(*UpdateTaskRequest)(nil),              // 42: carbon.rtc.UpdateTaskRequest
	(*UpdateTaskResponse)(nil),             // 43: carbon.rtc.UpdateTaskResponse
	(*structpb.Struct)(nil),                // 44: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),          // 45: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),            // 46: google.protobuf.Duration
	(*geo.Point)(nil),                      // 47: carbon.geo.Point
	(*geo.LineString)(nil),                 // 48: carbon.geo.LineString
	(*emptypb.Empty)(nil),                  // 49: google.protobuf.Empty
}
var file_proto_rtc_jobs_proto_depIdxs = []int32{
	1,  // 0: carbon.rtc.Objective.type:type_name -> carbon.rtc.Objective.ObjectiveType
	44, // 1: carbon.rtc.Objective.data:type_name -> google.protobuf.Struct
	6,  // 2: carbon.rtc.ObjectiveList.objectives:type_name -> carbon.rtc.Objective
	6,  // 3: carbon.rtc.ListObjectivesResponse.objectives:type_name -> carbon.rtc.Objective
	6,  // 4: carbon.rtc.GetNextActiveObjectiveResponse.objective:type_name -> carbon.rtc.Objective
	45, // 5: carbon.rtc.Task.started_at:type_name -> google.protobuf.Timestamp
	45, // 6: carbon.rtc.Task.ended_at:type_name -> google.protobuf.Timestamp
	46, // 7: carbon.rtc.Task.expected_duration:type_name -> google.protobuf.Duration
	21, // 8: carbon.rtc.Task.expected_tractor_state:type_name -> carbon.rtc.TractorState
	0,  // 9: carbon.rtc.Task.state:type_name -> carbon.rtc.State
	47, // 10: carbon.rtc.Task.start_location:type_name -> carbon.geo.Point
	47, // 11: carbon.rtc.Task.end_location:type_name -> carbon.geo.Point
	14, // 12: carbon.rtc.Task.sequence:type_name -> carbon.rtc.SequenceTask
	15, // 13: carbon.rtc.Task.manual:type_name -> carbon.rtc.ManualTask
	16, // 14: carbon.rtc.Task.go_to_and_face:type_name -> carbon.rtc.GoToAndFaceTask
	18, // 15: carbon.rtc.Task.follow_path:type_name -> carbon.rtc.FollowPathTask
	20, // 16: carbon.rtc.Task.tractor_state:type_name -> carbon.rtc.SetTractorStateTask
	13, // 17: carbon.rtc.Task.laser_weed:type_name -> carbon.rtc.LaserWeedTask
	12, // 18: carbon.rtc.Task.stop_autonomy:type_name -> carbon.rtc.StopAutonomyTask
	17, // 19: carbon.rtc.Task.go_to_reversible_path:type_name -> carbon.rtc.GoToReversiblePathTask
	48, // 20: carbon.rtc.LaserWeedTask.path:type_name -> carbon.geo.LineString
	25, // 21: carbon.rtc.LaserWeedTask.tolerances:type_name -> carbon.rtc.SpatialPathTolerance
	11, // 22: carbon.rtc.SequenceTask.items:type_name -> carbon.rtc.Task
	47, // 23: carbon.rtc.GoToAndFaceTask.point:type_name -> carbon.geo.Point
	48, // 24: carbon.rtc.GoToReversiblePathTask.path:type_name -> carbon.geo.LineString
	25, // 25: carbon.rtc.GoToReversiblePathTask.tolerances:type_name -> carbon.rtc.SpatialPathTolerance
	48, // 26: carbon.rtc.FollowPathTask.path:type_name -> carbon.geo.LineString
	19, // 27: carbon.rtc.FollowPathTask.speed:type_name -> carbon.rtc.SpeedSetting
	49, // 28: carbon.rtc.SpeedSetting.remote_operator_controlled:type_name -> google.protobuf.Empty
	49, // 29: carbon.rtc.SpeedSetting.implement_controlled:type_name -> google.protobuf.Empty
	21, // 30: carbon.rtc.SetTractorStateTask.state:type_name -> carbon.rtc.TractorState
	2,  // 31: carbon.rtc.TractorState.gear:type_name -> carbon.rtc.TractorState.Gear
	22, // 32: carbon.rtc.TractorState.hitch:type_name -> carbon.rtc.HitchState
	3,  // 33: carbon.rtc.HitchState.command:type_name -> carbon.rtc.HitchState.HitchCommand
	11, // 34: carbon.rtc.TaskList.tasks:type_name -> carbon.rtc.Task
	11, // 35: carbon.rtc.ListTasksResponse.tasks:type_name -> carbon.rtc.Task
	45, // 36: carbon.rtc.Job.started_at:type_name -> google.protobuf.Timestamp
	45, // 37: carbon.rtc.Job.ended_at:type_name -> google.protobuf.Timestamp
	6,  // 38: carbon.rtc.Job.objectives:type_name -> carbon.rtc.Objective
	0,  // 39: carbon.rtc.Job.state:type_name -> carbon.rtc.State
	4,  // 40: carbon.rtc.Job.type:type_name -> carbon.rtc.Job.JobType
	26, // 41: carbon.rtc.JobList.jobs:type_name -> carbon.rtc.Job
	26, // 42: carbon.rtc.ListJobsResponse.jobs:type_name -> carbon.rtc.Job
	45, // 43: carbon.rtc.WorkOrder.scheduled_at:type_name -> google.protobuf.Timestamp
	26, // 44: carbon.rtc.WorkOrder.jobs:type_name -> carbon.rtc.Job
	0,  // 45: carbon.rtc.WorkOrder.state:type_name -> carbon.rtc.State
	29, // 46: carbon.rtc.WorkOrderList.work_orders:type_name -> carbon.rtc.WorkOrder
	29, // 47: carbon.rtc.ListWorkOrdersResponse.work_orders:type_name -> carbon.rtc.WorkOrder
	0,  // 48: carbon.rtc.Intervention.state:type_name -> carbon.rtc.State
	5,  // 49: carbon.rtc.Intervention.cause:type_name -> carbon.rtc.Intervention.InterventionCause
	32, // 50: carbon.rtc.InterventionList.intervention:type_name -> carbon.rtc.Intervention
	32, // 51: carbon.rtc.ListInterventionsResponse.interventions:type_name -> carbon.rtc.Intervention
	32, // 52: carbon.rtc.CreateInterventionRequest.intervention:type_name -> carbon.rtc.Intervention
	32, // 53: carbon.rtc.CreateInterventionResponse.intervention:type_name -> carbon.rtc.Intervention
	47, // 54: carbon.rtc.GetActiveTaskRequest.current_location:type_name -> carbon.geo.Point
	11, // 55: carbon.rtc.GetActiveTaskResponse.task:type_name -> carbon.rtc.Task
	11, // 56: carbon.rtc.GetTaskResponse.task:type_name -> carbon.rtc.Task
	0,  // 57: carbon.rtc.UpdateTaskRequest.state:type_name -> carbon.rtc.State
	45, // 58: carbon.rtc.UpdateTaskRequest.started_at:type_name -> google.protobuf.Timestamp
	47, // 59: carbon.rtc.UpdateTaskRequest.start_location:type_name -> carbon.geo.Point
	45, // 60: carbon.rtc.UpdateTaskRequest.ended_at:type_name -> google.protobuf.Timestamp
	47, // 61: carbon.rtc.UpdateTaskRequest.end_location:type_name -> carbon.geo.Point
	11, // 62: carbon.rtc.UpdateTaskResponse.task:type_name -> carbon.rtc.Task
	36, // 63: carbon.rtc.JobService.CreateIntervention:input_type -> carbon.rtc.CreateInterventionRequest
	38, // 64: carbon.rtc.JobService.GetActiveTask:input_type -> carbon.rtc.GetActiveTaskRequest
	40, // 65: carbon.rtc.JobService.GetTask:input_type -> carbon.rtc.GetTaskRequest
	9,  // 66: carbon.rtc.JobService.GetNextActiveObjective:input_type -> carbon.rtc.GetNextActiveObjectiveRequest
	42, // 67: carbon.rtc.JobService.UpdateTask:input_type -> carbon.rtc.UpdateTaskRequest
	37, // 68: carbon.rtc.JobService.CreateIntervention:output_type -> carbon.rtc.CreateInterventionResponse
	39, // 69: carbon.rtc.JobService.GetActiveTask:output_type -> carbon.rtc.GetActiveTaskResponse
	41, // 70: carbon.rtc.JobService.GetTask:output_type -> carbon.rtc.GetTaskResponse
	10, // 71: carbon.rtc.JobService.GetNextActiveObjective:output_type -> carbon.rtc.GetNextActiveObjectiveResponse
	43, // 72: carbon.rtc.JobService.UpdateTask:output_type -> carbon.rtc.UpdateTaskResponse
	68, // [68:73] is the sub-list for method output_type
	63, // [63:68] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_proto_rtc_jobs_proto_init() }
func file_proto_rtc_jobs_proto_init() {
	if File_proto_rtc_jobs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_rtc_jobs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Objective); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectiveList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListObjectivesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveObjectiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextActiveObjectiveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopAutonomyTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaserWeedTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SequenceTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoToAndFaceTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoToReversiblePathTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowPathTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeedSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTractorStateTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HitchState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTasksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpatialPathTolerance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkOrderList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListWorkOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Intervention); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterventionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInterventionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInterventionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInterventionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInterventionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActiveTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_rtc_jobs_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_proto_rtc_jobs_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Task_Sequence)(nil),
		(*Task_Manual)(nil),
		(*Task_GoToAndFace)(nil),
		(*Task_FollowPath)(nil),
		(*Task_TractorState)(nil),
		(*Task_LaserWeed)(nil),
		(*Task_StopAutonomy)(nil),
		(*Task_GoToReversiblePath)(nil),
	}
	file_proto_rtc_jobs_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*SpeedSetting_ConstantMph)(nil),
		(*SpeedSetting_RemoteOperatorControlled)(nil),
		(*SpeedSetting_ImplementControlled)(nil),
	}
	file_proto_rtc_jobs_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*TractorState_Gear_)(nil),
		(*TractorState_Hitch)(nil),
	}
	file_proto_rtc_jobs_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*HitchState_Command)(nil),
		(*HitchState_Position)(nil),
	}
	file_proto_rtc_jobs_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_proto_rtc_jobs_proto_msgTypes[36].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_rtc_jobs_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_rtc_jobs_proto_goTypes,
		DependencyIndexes: file_proto_rtc_jobs_proto_depIdxs,
		EnumInfos:         file_proto_rtc_jobs_proto_enumTypes,
		MessageInfos:      file_proto_rtc_jobs_proto_msgTypes,
	}.Build()
	File_proto_rtc_jobs_proto = out.File
	file_proto_rtc_jobs_proto_rawDesc = nil
	file_proto_rtc_jobs_proto_goTypes = nil
	file_proto_rtc_jobs_proto_depIdxs = nil
}
