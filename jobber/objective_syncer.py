import asyncio
from contextlib import AbstractAsyncContextManager
from types import TracebackType
from typing import TYPE_CHECKING, List, Optional, Type

import grpc

from generated.proto.rtc import jobs_pb2, jobs_pb2_grpc
from lib.cloud.auth import TokenStore
from lib.cloud.grpc import GrpcConf, get_channel
from lib.cloud.services import RTC_JOBS_GRPC_HOST, RTC_JOBS_GRPC_PORT
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.environment import robot_name
from lib.common.logging import get_logger

LOG = get_logger(__name__)


if TYPE_CHECKING:
    ACM = AbstractAsyncContextManager["ObjectiveSyncer"]
    TASK_TYPE = asyncio.Task[None]
else:
    ACM = AbstractAsyncContextManager
    TASK_TYPE = asyncio.Task

LONG_POLL_TIMEOUT = 60
RECONNECT_WAIT = 5


class ObjectiveSyncer(ACM):
    def __init__(self, ts: TokenStore, conf: Optional[GrpcConf] = None) -> None:
        super().__init__()
        self._ts = ts
        if conf is None:
            self._conf = GrpcConf(RTC_JOBS_GRPC_HOST, RTC_JOBS_GRPC_PORT)
        else:
            self._conf = conf

        self._stop_event = asyncio.Event()
        self._tasks: List[TASK_TYPE] = []
        self._active_objective = 0

    @property
    def active_objective(self) -> int:
        return self._active_objective

    async def __get_next(self, stub: jobs_pb2_grpc.JobServiceStub) -> jobs_pb2.GetNextActiveObjectiveResponse:
        resp: jobs_pb2.GetNextActiveObjectiveResponse = await stub.GetNextActiveObjective(
            jobs_pb2.GetNextActiveObjectiveRequest(objective_id=self._active_objective),
            timeout=LONG_POLL_TIMEOUT,
            metadata=(("robot", robot_name()),),
        )
        return resp

    async def _sync(self) -> None:
        while not self._stop_event.is_set():
            async with get_channel(self._conf, await self._ts.fetch()) as chan:
                stub = jobs_pb2_grpc.JobServiceStub(chan)
                while not self._stop_event.is_set():
                    try:
                        resp = await cancellable_await(self.__get_next(stub), self._stop_event)
                        if resp is None:
                            break
                        LOG.info(f"Active objective has changed from {self._active_objective} to {resp.objective.id}")
                        self._active_objective = resp.objective.id
                    except grpc.aio.AioRpcError as exc:
                        if exc.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                            # common case of no new objective
                            continue
                        LOG.exception("Unknown grpc exception getting next active objective")
                    except Exception:
                        LOG.exception("Unknown exception getting next active objective")
                    else:
                        continue
                    await cancellable_await(asyncio.sleep(RECONNECT_WAIT), self._stop_event)
                    break  # rebuild the whole channel

    async def __aenter__(self) -> "ObjectiveSyncer":
        self._stop_event.clear()
        self._tasks.append(asyncio.get_event_loop().create_task(self._sync()))
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        self._stop_event.set()
        await asyncio.gather(*self._tasks)
