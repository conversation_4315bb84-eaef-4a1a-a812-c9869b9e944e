import logging
import os
from typing import Any

import numpy as np
import torch
import torchvision
from PIL import Image, ImageDraw
from torch.distributed.launcher.api import LaunchConfig

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]
CHIP_SIZE = 400


class default_transform_fn:
    def __init__(self, chip_size: int = 400) -> None:
        self.chip_size = chip_size

    def __call__(self, image: Image.Image) -> torch.Tensor:
        tensor_image: torch.Tensor
        tensor_image = torchvision.transforms.functional.to_tensor(image)
        tensor_image = torchvision.transforms.functional.resize(tensor_image, (CHIP_SIZE, CHIP_SIZE), antialias=None)
        tensor_image = torchvision.transforms.functional.normalize(tensor_image, IMAGENET_MEAN, IMAGENET_STD)

        return tensor_image


class crop_transform_fn:
    def __init__(self, chip_size: int = 400) -> None:
        self.chip_size = chip_size

    def __call__(self, image: Image.Image) -> torch.Tensor:
        tensor_image: torch.Tensor
        tensor_image = torchvision.transforms.functional.to_tensor(image)
        tensor_image = torchvision.transforms.functional.center_crop(tensor_image, (CHIP_SIZE, CHIP_SIZE))
        tensor_image = torchvision.transforms.functional.normalize(tensor_image, IMAGENET_MEAN, IMAGENET_STD)

        return tensor_image


def default_load_fn(filepath: str) -> Image.Image:
    with Image.open(filepath) as image:
        image = image.convert("RGB")

    return image


def create_text_image(text: str, width: int = 150, height: int = 50) -> Any:
    img = Image.new("RGB", (width, height), color="white")
    d = ImageDraw.Draw(img)
    d.text((10, 10), text, fill=(0, 0, 0))
    return np.array(img)


def get_elastic_launcher_config(num_gpus: int = 8) -> LaunchConfig:
    launch_config = LaunchConfig(
        min_nodes=1,
        max_nodes=1,
        nproc_per_node=num_gpus,
        run_id="none",
        role="default",
        rdzv_endpoint="127.0.0.1:29500",
        rdzv_backend="static",
        rdzv_configs={"rank": 0, "timeout": 900},
        rdzv_timeout=-1,
        max_restarts=0,
        monitor_interval=5,
        start_method="spawn",
        metrics_cfg={},
        local_addr=None,
    )
    return launch_config


def save_model(data_dir: str, model: torch.nn.Module, save_best: bool) -> None:
    LOG = logging.getLogger("selfsup")

    weights_dir = os.path.join(data_dir, "weights")
    latest_filepath = os.path.join(weights_dir, "latest.pt")
    best_filepath = os.path.join(weights_dir, "best.pt")

    os.makedirs(weights_dir, exist_ok=True)

    LOG.info(f"Saving model to {latest_filepath}")
    torch.save(model.state_dict(), latest_filepath)

    if save_best:
        LOG.info(f"Saving model to {best_filepath}")
        torch.save(model.state_dict(), best_filepath)
