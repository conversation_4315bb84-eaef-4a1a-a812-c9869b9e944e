from typing import Any

import numpy as np
import torch
import wandb

from ..losses import quadruplet_loss
from ..models.base import BaseEncoder
from ..utilities import IMAGENET_MEAN, IMAGENET_STD, create_text_image
from .base import BaseModel

COMPATIBLE_LOSSES = ["quadruplet", "triplet"]


class SimpleContrastiveModel(BaseModel):
    def __init__(
        self, encoder: BaseEncoder, loss_fn: str = "quadruplet", distance: str = "euclidean", margin: float = 0.5
    ):
        super().__init__()

        self._encoder = encoder
        self._loss_fn_name = loss_fn
        self._distance = distance
        self._margin = margin

        if distance not in ["euclidean", "cosine"]:
            raise ValueError(f"Distance function must be either 'euclidean' or 'cosine'. Got: {distance}")

        if loss_fn not in COMPATIBLE_LOSSES:
            raise ValueError(f"Unknown loss function: {loss_fn}")

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def get_image_sample(self, *args: torch.Tensor) -> Any:
        assert len(args) == 4, f"Expected 4 inputs, got {len(args)}"
        x1, x2, x1_t, x2_t = args
        batch_idx = np.random.randint(0, x1.shape[0])
        x1, x2, x1_t, x2_t = x1[batch_idx], x2[batch_idx], x1_t[batch_idx], x2_t[batch_idx]

        print(x1.mean(), x2.mean(), x1_t.mean(), x2_t.mean())
        rank = torch.distributed.get_rank()
        x1 = x1.unsqueeze(0).to(f"cuda:{rank}")
        x2 = x2.unsqueeze(0).to(f"cuda:{rank}")
        x1_t = x1_t.unsqueeze(0).to(f"cuda:{rank}")
        x2_t = x2_t.unsqueeze(0).to(f"cuda:{rank}")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)

        emb_1_t = self._encoder(x1_t)
        emb_2_t = self._encoder(x2_t)

        imagenet_mean = torch.tensor(IMAGENET_MEAN).reshape(-1, 1, 1).to(f"cuda:{rank}")
        imagenet_std = torch.tensor(IMAGENET_STD).reshape(-1, 1, 1).to(f"cuda:{rank}")

        out = [
            wandb.Image((x1[0] * imagenet_std) + imagenet_mean, caption="Sample 1"),
            wandb.Image((x2[0] * imagenet_std) + imagenet_mean, caption="Sample 2"),
            wandb.Image((x1_t[0] * imagenet_std) + imagenet_mean, caption="Sample 1 Transformed"),
            wandb.Image((x2_t[0] * imagenet_std) + imagenet_mean, caption="Sample 2 Transformed"),
        ]

        s1_s2 = (emb_1 @ emb_2.T) / (emb_1.norm() * emb_2.norm())
        s1_t_s2_t = (emb_1_t @ emb_2_t.T) / (emb_1_t.norm() * emb_2_t.norm())
        s1_s1_t = (emb_1 @ emb_1_t.T) / (emb_1.norm() * emb_1_t.norm())

        caption = (
            f"d(S1, S2): {s1_s2.item():.4f}\nd(S1_t, S2_t): {s1_t_s2_t.item():.4f}\nd(S1, S1_t): {s1_s1_t.item():.4f}"
        )
        out.append(wandb.Image(create_text_image(caption), caption="Cosine Similarity"))
        return out

    def forward(self, *args: torch.Tensor) -> torch.Tensor:
        assert len(args) == 4, f"Expected 4 inputs, got {len(args)}"
        x1, x2, x1_t, x2_t = args

        rank = torch.distributed.get_rank()
        x1 = x1.to(f"cuda:{rank}")
        x2 = x2.to(f"cuda:{rank}")
        x1_t = x1_t.to(f"cuda:{rank}")
        x2_t = x2_t.to(f"cuda:{rank}")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)

        emb_1_t = self._encoder(x1_t)
        emb_2_t = self._encoder(x2_t)

        if self._loss_fn_name == "quadruplet":
            loss = quadruplet_loss(emb_1, emb_2, emb_1_t, emb_2_t, margin=self._margin, distance=self._distance)
        elif self._loss_fn_name == "triplet":
            distance_function = (
                lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
                if self._distance == "cosine"
                else torch.nn.PairwiseDistance()(x, y)
            )
            loss = torch.nn.functional.triplet_margin_with_distance_loss(
                emb_1, emb_1_t, emb_2, margin=self._margin, distance_function=distance_function
            )

        return loss
