from typing import Optional

import pydantic


class TrainingConfig(pydantic.BaseModel):
    learning_rate: float = 1e-3
    num_epochs: int = 100
    batch_size: int = 16
    num_samples: int = 20000  # Per rank
    num_validation_samples: int = 2000
    prefetch_factor: int = 2
    num_workers: int = 2
    data_dir: str
    wandb_project: str
    model_id: str
    description: Optional[str]
    num_gpus: int
