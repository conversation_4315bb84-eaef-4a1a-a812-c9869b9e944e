import json
import logging
import os
import time
from typing import Any, Callable, List, Optional

import numpy as np
import pydantic
import tensorrt as trt
import torch
import torchvision.transforms as TF
import tqdm
from PIL import Image
from scipy.ndimage import laplace, sobel
from torch2trt import torch2trt

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.tensorrt import save_tensorrt_model
from deeplearning.scripts.utils.utils import get_dataset_v2
from deeplearning.self_supervised.datasets import FourChipDataset
from deeplearning.self_supervised.encoders import BaseEncoder, ResNet
from deeplearning.self_supervised.models import BaseModel, SimpleContrastiveModel
from deeplearning.self_supervised.utilities import IMAGENET_MEAN, IMAGENET_STD, crop_transform_fn, default_transform_fn
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)

MIN_RADIUS = 1


class Config(pydantic.BaseModel):
    model_id: str
    num_epochs: int = 100
    num_samples: int = 10000
    num_validation_samples: int = 2000
    pretrained: bool = True
    num_workers: int = 2
    backbone: str = "resnet50"
    learning_rate: float = 1e-4
    description: Optional[str] = None
    model_type: str = "SimpleContrastive"
    wandb_project: str
    num_gpus: int
    fast_run: bool
    data_dir: str
    loss_fn: str = "triplet"
    distance: str = "cosine"
    use_radius: bool = True
    resize_method: str = "interpolation"  # interpolation or crop
    batch_size: int = 16
    chip_size: int = 400

    # Triplet/Quadruplet loss:
    margin: float = 0.5


def sobel_filter(img: Image) -> Any:
    img = np.array(img).astype(np.float32)
    h = sobel(img, axis=0)
    v = sobel(img, axis=1)
    magnitude = np.sqrt(h ** 2 + v ** 2)
    magnitude *= 255.0 / np.max(magnitude)

    return magnitude


def laplacian(img: Image) -> Any:
    return laplace(img)


BASE_TRANSFORMS = [
    TF.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
    TF.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.5),
    TF.Grayscale(num_output_channels=3),
    TF.RandomHorizontalFlip(),
    TF.RandomVerticalFlip(),
    TF.RandomRotation(45),
    laplacian,
]

MODEL_DATASET_MAP = {
    "SimpleContrastive": FourChipDataset,
}


S3_CLIENT = S3CacheProxyClient(s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"))


class s3_cache_load_fn:
    def __init__(self, use_radius: bool = True, chip_size: int = 400):
        self.use_radius = use_radius
        self.chip_size = chip_size

    def __call__(self, info: Any) -> Image.Image:
        uri = info["uri"]
        x = info["x"]
        y = info["y"]
        radius = info["radius"]
        bucket, key = S3_CLIENT.split_uri(uri)
        return S3_CLIENT._get_image_subset_from_s3_cache_proxy(
            bucket=bucket, key=key, x=x, y=y, radius=radius if self.use_radius else self.chip_size // 2, fill=True,
        )


def get_model(config: Config) -> BaseModel:
    if config.backbone.lower().startswith("resnet"):
        encoder = ResNet(architecture=config.backbone, pretrained=config.pretrained, final_relu=False)
    else:
        raise ValueError(f"Unknown backbone: {config.backbone}")

    if config.model_type.lower() == "SimpleContrastive".lower():
        model = SimpleContrastiveModel(
            encoder=encoder, loss_fn=config.loss_fn, distance=config.distance, margin=config.margin
        )
    else:
        raise ValueError(f"Unknown model type: {config.model_type}")

    return model


def get_transform_fn(config: Config) -> Callable[[Any], torch.Tensor]:
    if config.resize_method == "crop":
        return crop_transform_fn(chip_size=config.chip_size)
    elif config.resize_method == "interpolation":
        return default_transform_fn(chip_size=config.chip_size)
    else:
        raise ValueError(f"Unknown resize method: {config.resize_method}")


def get_datasets(
    config: Config, dataset_id: Optional[str] = None, pipeline_id: Optional[str] = None, exist_ok: bool = False
) -> List[Any]:
    size_limit = 1000 if config.fast_run else None
    LOG.info("Fetching dataset...")
    get_dataset = False
    if exist_ok:
        for split in ["train", "validation", "test"]:
            if not os.path.exists(os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{split}.jsonl")):
                get_dataset = True
                break

    if get_dataset or not exist_ok:
        dataset_id, _ = get_dataset_v2(
            dataset_id,
            pipeline_id,
            train_set_size_limit=size_limit,
            validation_set_size_limit=size_limit,
            test_set_size_limit=size_limit,
        )

    LOG.info(f"Dataset ID: {dataset_id}")
    datasets = []

    for split in ["train", "validation", "test"]:
        LOG.info(f"Processing dataset split: {split}")
        assert split is not None, f"Dataset split {split} does not exist"
        split_json_path = os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{split}.jsonl")
        assert os.path.exists(split_json_path), f"Dataset split {split_json_path} does not exist"

        data = []

        with open(split_json_path) as f:
            for line in tqdm.tqdm(f):
                image = json.loads(line)

                for point in image["points"]:
                    if point["radius"] > MIN_RADIUS:
                        info = {
                            "uri": image["uri"],
                            "x": point["x"],
                            "y": point["y"],
                            "radius": point["radius"],
                        }

                        data.append(info)
        dataset = MODEL_DATASET_MAP[config.model_type](
            data,
            load_fn=s3_cache_load_fn(use_radius=config.use_radius, chip_size=config.chip_size),
            transform_fn=get_transform_fn(config),
            transform_list=BASE_TRANSFORMS,
        )

        datasets.append(dataset)

    return datasets


class DatasetWrapper:
    def __init__(self, dataset: torch.utils.data.Dataset[Any], num_samples: Optional[int] = None) -> None:
        self._dataset = dataset
        self._num_samples = num_samples

    def __getitem__(self, index: int) -> Any:
        item = self._dataset[index]
        if isinstance(item, torch.Tensor):
            return item.cuda()
        return self._dataset[index][0].unsqueeze(0).cuda()

    def __len__(self) -> int:
        return len(self._dataset) if not self._num_samples else self._num_samples  # type: ignore


def convert_trt(
    encoder: BaseEncoder,
    calib_dataset: DatasetWrapper,
    validation_dataset: DatasetWrapper,
    config: Config,
    trt_path: str,
    max_batch_size: int = 3,
    calibration_batch_size: int = 16,
) -> None:

    test_input = [calib_dataset[0].repeat(max_batch_size, 1, 1, 1)]

    encoder.eval().cuda()

    pyt_embeddings = []
    pyt_timing = []
    for i in range(min(20, len(validation_dataset))):
        start = time.time()
        pyt_embeddings.append(encoder(validation_dataset[i].cuda()))
        end = time.time()
        if i > 2:  # warmup
            pyt_timing.append(end - start)

    stacked_pyt_embeddings = torch.cat(pyt_embeddings, dim=0)
    stacked_pyt_embeddings /= stacked_pyt_embeddings.norm(dim=1, keepdim=True)

    with torch.no_grad():
        trt_encoder = torch2trt(
            encoder,
            test_input,
            fp16_mode=False,
            int8_mode=False,
            int8_calib_dataset=calib_dataset,
            int8_calib_batch_size=calibration_batch_size,
            max_batch_size=max_batch_size,
            max_workspace_size=1 << 22,
            log_level=trt.Logger.INFO,
            strict_type_constraints=True,
            use_implicit_batch_dimension=False,
        )

    trt_embeddings = []
    trt_timing = []
    for i in range(min(20, len(validation_dataset))):
        start = time.time()
        emb = trt_encoder(validation_dataset[i].cuda())
        trt_embeddings.append(emb[0, :].unsqueeze(0))
        end = time.time()
        if i > 2:  # warmup
            trt_timing.append(end - start)
    stacked_trt_embeddings = torch.cat(trt_embeddings, dim=0)
    stacked_trt_embeddings /= stacked_trt_embeddings.norm(dim=1, keepdim=True)

    cos_sims = stacked_pyt_embeddings @ stacked_trt_embeddings.T
    embedding_error = torch.diag(cos_sims, 0)

    print("Embedding error (Cosine similarity): ")
    print(f"\tmean: {embedding_error.mean():.5f}")
    print(f"\tmedian: {torch.median(embedding_error):.5f}")
    print(f"\tmin: {embedding_error.min():.5f}")
    print(f"\tmax: {embedding_error.max():.5f}")
    print(f"\t99%tile: {torch.quantile(embedding_error, 0.99):.5f}")

    print(f"Average pre-cvt time: {1000 * sum(pyt_timing) / len(pyt_timing):.5f} ms")
    print(f"Average post-cvt time: {1000 * sum(trt_timing) / len(trt_timing):.5f} ms")

    metadata = ModelMetadata(
        input_dtype=torch.float32,
        input_size=test_input[0].shape[2:],
        supports_half=False,
        trained_embeddings=True,
        backbone_architecture=config.backbone,
        model_class=config.model_type,
        max_batch_size=max_batch_size,
        means=IMAGENET_MEAN,
        stds=IMAGENET_STD,
    )

    save_tensorrt_model(trt_encoder, metadata, trt_path)
